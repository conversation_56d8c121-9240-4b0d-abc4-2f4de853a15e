/******************** (C) COPYRIGHT 2022     ***********************************
* File Name          : app_bsm_com_outernet.c
* Author             : dfy
* Version            : V1.0
* Date               : 2022.07.07
* Description        : ĺ¤çéďż˝??
********************************************************************************/
#include "app_bsm_com_outernet.h"
#include "crc.h"
#include <dfs_posix.h>
#include "uapp.h"


//#define BSM_COM_DEBUG

#ifdef BSM_COM_DEBUG
#define BSM_COM_PRINT(...)  rt_kprintf(__VA_ARGS__)
#else
#define BSM_COM_PRINT(...) 
#endif


#define FW_BUFF_LEN  1024
typedef struct{
	uint8_t 	*rx_buff;
	uint8_t 	*tx_buff;
	uint8_t 	*ack_buff;
	
	uint32_t 	rx_len;
	uint32_t    max_data_len;
	uint8_t 	com_prot;
}BSM_OPT_T;

typedef struct{
    uint32_t 	funcid_0x0A;
	uint32_t 	funcid_0x81;
	uint32_t 	funcid_0x82; 	
	uint32_t 	funcid_0x83; 	
	uint32_t 	funcid_0x84;
    uint32_t 	funcid_0x85;
    uint32_t 	funcid_0x86;
    uint32_t 	funcid_0x87;
    uint32_t 	funcid_0x88;
    uint32_t 	funcid_0x21;
    uint32_t 	funcid_0x22;
}rxptu_cnter_t;

 rxptu_cnter_t bsm_com_rxcnter;
 rxptu_cnter_t bsm_com_txcnter;
extern UART_HandleTypeDef huart2;


//ćä˝çłťçťĺé
static struct rt_semaphore bsm_sem_rev = {0};

static BsmFrame0A Bsm_FwInfo 		    	= {0};      //ĺçş§ĺşäťśäżĄćŻďż??
static BsmFrame0B Bsm_FwUpdate 				= {0};   	//ĺşäťśďż??

uint8_t bsm_com_port = 0;

//çźĺ˛ĺşĺďż??
static uint32_t Bsm_Rx_Lenth = 0; 				     //čŽ°ĺ˝ćĽćśçéżďż??
static uint8_t  APP_BSM_Buff_Tx [ BSM_DATA_TX_LENTH ];  //ĺéçźĺ­ĺşć°çť
static uint8_t  APP_BSM_Buff_Rx [ BSM_DATA_RX_LENTH ];  //ćĽćśçźĺ­ĺşć°ďż??
static uint8_t  APP_BSM_Ack_Buff   [ BSM_DATA_TX_LENTH ];  //ĺşç¨ĺąçźĺ˛ĺ¨


static char  APP_FW_DATA_Buff [ FW_BUFF_LEN ];  		 //ćĽćśçźĺ­ĺşć°ďż??

//ć ĺżďż??
static uint8_t bin_upgrade_step = 0;  //ĺşäťśćĽćśĺŽć ĺżä˝


BSM_OPT_T bsm_opt = {
	.rx_buff		= APP_BSM_Buff_Rx,     
	.tx_buff		= APP_BSM_Buff_Tx,
	.ack_buff		= APP_BSM_Ack_Buff,
	.com_prot		= 0,
	.rx_len			= 0,
	.max_data_len 	= BSM_DATA_RX_LENTH,
};

//ĺ˝ć°ĺŁ°ć
static int app_bsm_com_rx(uint8_t Dev_Addr,const uint8_t *pRxBuff,uint16_t Size);

extern void esp01_send_data(const uint8_t *pData, uint16_t len);
extern void rs485_send_frame(const void *buff, int size);
extern void fdcan_ptu_send_data(const uint8_t *pSendData, uint16_t Size);
/**
  * @brief äťćĽĺŁďż˝?ďż˝ĺść°ćŽćľĺ°ĺşç¨ďż??
  * @param 
  * @retval 
  */
void app_bsm_com_rev_byte(const uint8_t *pData,rt_size_t size)
{
    uint8_t result = -1;
    
	if (size > 0 && size < bsm_opt.max_data_len)
	{				
        result = api_bsm_check_frame((uint8_t*)pData);
        
		if (0 == result) 		// ćŁćĽĺ¸§ďż??
		{	
            result = app_bsm_com_rx(DEV_ADDR, pData, size);
            
			if (0 == result) // ĺ¤ć­ĺ°ĺĺć Ąďż??
			{			
                if(DEV_ADDR == pData[3])
                    vfd.modbus_id_lock = 1;
                
				rt_memset(bsm_opt.rx_buff, 0, bsm_opt.max_data_len);   
				
				rt_memcpy(bsm_opt.rx_buff, pData, size);
				
				bsm_opt.rx_len = size;			// - čŽ°ĺ˝ćĽćśçéżďż??
				bsm_opt.rx_buff[size] = 0;		// - çźĺ˛ĺşćĺä¸ä˝ć¸0
				
				rt_sem_release(&bsm_sem_rev);
			}          
		}			
	}
    
    
}
extern rt_uint8_t rt_kprintf_lock;
/**
  * @brief ĺéć°ďż??ćľďż˝?ďż˝ç
  * @param 
  * @retval 
  */
static void app_bsm_com_tx(const uint8_t *pData,uint16_t Size)
{
	if(NULL == pData || 0 == Size)	return ;
	
	switch(bsm_com_port)
	{
		case dev_485:
			rs485_send_frame(pData, Size);
			break;
        #ifdef  PKG_USING_AT_DEVICE
		case dev_esp:
			esp01_send_data(pData, Size);
			break;
        #endif
		case dev_can:
			fdcan_ptu_send_data(pData, Size);
			break;
        case dev_console:
            toolbox_dma_printf_binary((uint8_t *)pData,Size);
        
            break;
	
	}
	//rt_memset(bsm_opt.ack_buff, 0, sizeof(APP_BSM_Buff_Tx));
}

/**
  * @brief ćĽćść°ćŽćľďż˝?ďż˝ç
  * @param 
  * @retval 
  */
static int app_bsm_com_rx(uint8_t Dev_Addr,const uint8_t *pRxBuff,uint16_t Size)
{
	int ret = 0; //čżĺďż??
	
	ret = api_bsm_check_addr_crc(Dev_Addr, pRxBuff, Size);
	
	if (-1 == ret)
	{
//		if (2 == bsm_com_port)
//			app_bsm_com_tx(pRxBuff, Size);  
//				
		return ret;
	}		
	else if (-2 == ret )
	{
		return -2;
	}
	
	return ret;		
}

//----------------------------------------------------------------------------
// ĺśäťĺ˝ć°ĺč˝ďż??
//----------------------------------------------------------------------------

/**
  * @brief  check crc ć ĄéŞbinćäťśĺŽć´ďż??
  * @param  filename
  * @retval result
  */
static int app_bin_crc_check(const char *f_name)
{
    int fd, len;
    uint16_t crc = 0xFFFF;
    char *bufs = &APP_FW_DATA_Buff[0];

    if ((fd = open(f_name, O_RDONLY | O_BINARY, 0666)) < 0)
    {
        rt_kprintf("open %s er.\r\n", f_name);
        return -1;
    }
    do
    {
        memset(bufs, 0, FW_BUFF_LEN);
        if ((len = read(fd, bufs, FW_BUFF_LEN)) > 0)
        {
            crc = CRC16_A001((void *)bufs, len, crc);
        }
    }
    while (len > 0);

    close(fd);
    return crc == 0 ? 0 : -1;
}

/**
  * @brief  app_send_data 
  * @param  
  * @retval 
  */
static uint16_t app_bsm_send_data(	uint8_t dev_addr,
									uint8_t func_frame,
									uint16_t data_lenth,
									uint16_t data_offset,
									void *pSendData,
									uint8_t * sendBuff)
{
    
    com_cnter_send(&com_ptu);
	// 1.ĺ°ďż˝?ďż˝ĺďż??ć°ćŽ
	uint16_t sendSize = api_bsm_com_package(	dev_addr,       	//čŽžďż˝?ďż˝ĺ°ĺ
												func_frame,			//ĺ¸§ĺč˝ĺˇ
												data_lenth,  	    //ć°ćŽĺ¤§ĺ°
												data_offset,        //ć°ćŽĺç§ťďż??
												pSendData,          //ć°ćŽďż??
												sendBuff);	  		//ĺéçźĺ˛ĺş
	// 2.ĺéć°ďż??
	app_bsm_com_tx(sendBuff, sendSize);
	
	return sendSize;

}
//----------------------------------------------------------------------------
// - ĺĺşĺ¸§ĺ˝ć°ĺş
//----------------------------------------------------------------------------
/**
  * @brief F1.čˇĺçćŹďż??
  */
void bsm_unpack_get_version(void *frame)
{
	if(NULL == frame)
		return;
	
	BsmFramePackage *pBsmFP = (BsmFramePackage*)frame;
	BsmFrame01  *pSendStream = (BsmFrame01*)bsm_opt.ack_buff;
	{
	}	
	uint16_t sendSize = app_bsm_send_data(		pBsmFP->Dev_Addr,  	 //čŽžďż˝?ďż˝ĺ°ĺ
												pBsmFP->Frame_Fun, 	 //ĺ¸§ĺč˝ĺˇ
												sizeof(BsmFrame01),  //ć°ćŽĺ¤§ĺ°
												0,                   //ć°ćŽĺç§ťďż??
												pSendStream,         //ć°ćŽďż??
												bsm_opt.tx_buff);	 //ĺéçźĺ˛ĺş
}


/**
  * @brief F2.čˇĺćĽćďż??
  */
void bsm_unpack_get_date(void *frame)
{
	if(NULL == frame)
		return;

	BsmFramePackage *pBsmFP = (BsmFramePackage*)frame;
	BsmFrame02  *pSendStream = (BsmFrame02*)bsm_opt.ack_buff;
	{			
		pSendStream->Year  = vfd.date.year; 
		pSendStream->Month = vfd.date.month;
		pSendStream->Day   = vfd.date.day;  
		pSendStream->Hour  = vfd.date.hour; 
		pSendStream->Min   = vfd.date.min;  
		pSendStream->Sec   = vfd.date.sec; 
	}
	uint16_t sendSize = app_bsm_send_data(		pBsmFP->Dev_Addr,    //čŽžďż˝?ďż˝ĺ°ĺ
												pBsmFP->Frame_Fun,   //ĺ¸§ĺč˝ĺˇ
												sizeof(BsmFrame02),  //ć°ćŽĺ¤§ĺ°
												0,                   //ć°ćŽĺç§ťďż??
												pSendStream,         //ć°ćŽďż??
												bsm_opt.tx_buff);	 //ĺéçźĺ˛ĺş
}

/**
  * @brief F3.čˇĺć°ĺ­éčžĺĽĺ¸§
  */
void bsm_unpack_get_digital_input(void *frame)
{
	if(NULL == frame)	return;		
	BsmFramePackage *pBsmFP 		= (BsmFramePackage*)frame;;
}

/**
  * @brief F4.čˇĺčžĺşć°ĺ­éĺ¸§
  */
void bsm_unpack_get_digital_out(void *frame)
{
	if(NULL == frame)	return;
	BsmFramePackage *pBsmFP = (BsmFramePackage*)frame;
}

/**
  * @brief F5.čˇĺć¨Ąćééĺźĺ¸§
  */
void bsm_unpack_get_adc_value(void *frame)
{
	if(NULL == frame)	return;	
	BsmFramePackage *pBsmFP = (BsmFramePackage*)frame;
}

/**
  * @brief F6.
  */
void bsm_unpack_reset_mcu(void *frame)
{
    if(NULL == frame)	return;
	BsmFramePackage *pBsmFP = (BsmFramePackage*)frame;
    uint8_t  *pSendStream 	= (uint8_t *)bsm_opt.ack_buff;
    int ack_size = 0;

    
    
	uint16_t sendSize = app_bsm_send_data(		pBsmFP->Dev_Addr,	//čŽžďż˝?ďż˝ĺ°ĺ
												pBsmFP->Frame_Fun, 	//ĺ¸§ĺč˝ĺˇ
												ack_size, 			//ć°ćŽĺ¤§ĺ°
												0,                    				//ć°ćŽĺç§ťďż??
												bsm_opt.ack_buff,        				//ć°ćŽďż??
												bsm_opt.tx_buff);	  				//ĺéçźĺ˛ĺş
    
    vfd.bit.soft_stop = 1;
    BSM_COM_PRINT("\r\n get reset cmd,delay 1s to reset mcu ");
            
    rt_thread_mdelay(1 * RT_TICK_PER_SECOND); 
    
    rt_hw_cpu_reset(); 	
}
static uint16_t offset_writed = 0;
/**
  * @brief F0A.äżĺ­ĺşäťśäżĄćŻďż??
  */
void bsm_unpack_fw_info(void *frame)
{
	BsmFramePackage *pBsmFP = (BsmFramePackage*)frame;
	BsmFrame0A *pF0A = (BsmFrame0A *)pBsmFP->pData;
	
	rt_memset(&Bsm_FwInfo ,			0, sizeof(Bsm_FwInfo));
	rt_memset(&Bsm_FwUpdate.Ack, 	0, sizeof(Bsm_FwUpdate.Ack));
	rt_memcpy(&Bsm_FwInfo, 	  	 pF0A, sizeof(Bsm_FwInfo.Rev));
	int  fd;
	if(vfd.board_id == Bsm_FwInfo.Rev.FWSW)
    {
        vfd.is_update_fw = 1;

        unlink( BIN_FILE_NAME );            // - ĺ é¤ć§çBINćäťś

        Bsm_FwInfo.Ack.FWSW = vfd.board_id;  // - éĺĺşäťśĺçş§
        Bsm_FwInfo.Ack.EraseFlashFlag = 1;  // - ćŚé¤FLASHĺşćĺ?

        if( ( fd = open( BIN_FILE_NAME, O_CREAT ) ) < 0 ) {
            rt_kprintf( "open %s err.1\r\n", BIN_FILE_NAME );

        } else {
            Bsm_FwInfo.Ack.SaveBinFileInfoFlag = 1;  // - äżĺ­ĺşäťśäżĄćŻćĺ
            bin_upgrade_step = FW_FILE_DATA;         // - ć čŽ°ĺźĺ§ćĽćśćäť?
            offset_writed = 0;
        }

        record_logdata_push( LOG_Inv_FW_Info,Bsm_FwInfo.Rev.BinVersionNum%100 );
        close( fd );

	}
    else
    {
        Bsm_FwInfo.Ack.FWSW = vfd.board_id;
    }
	uint16_t sendSize = app_bsm_send_data(		DEV_ADDR,       	  		//čŽžďż˝?ďż˝ĺ°ĺ
												UNPACK_FW_INFO_F0A,  		//ĺ¸§ĺč˝ĺˇ
												sizeof(Bsm_FwInfo.Ack),  //ć°ćŽĺ¤§ĺ°
												0,                    		//ć°ćŽĺç§ťďż??
												&Bsm_FwInfo.Ack,         //ć°ćŽďż??
												bsm_opt.tx_buff);	  		//ĺéçźĺ˛ĺş
}

uint32_t bin_framecnt = 0;
/**
  * @brief F0B.ĺşäťść´ć°ďż??
  */
void bsm_unpack_fw_update(void *frame)
{	
	BsmFramePackage *pBsmFP = (BsmFramePackage*)frame;
    
	rt_uint16_t offset = pBsmFP->Data_offset;
    rt_uint16_t lenth  = pBsmFP->Data_Lenth;
	rt_memcpy(&Bsm_FwUpdate.Rev.BinFileFrameData, pBsmFP->pData, pBsmFP->Data_Lenth);	
	
	int  fd;
    
    if(vfd.board_id == Bsm_FwInfo.Rev.FWSW)
    {
        vfd.is_update_fw = 1;

        do
        {
            switch(bin_upgrade_step)
            {
                // - 1.1 ćĽćśĺşäťśĺ¸?
                case FW_FILE_DATA:
                    {		
                        Bsm_FwUpdate.Ack.Is_OneFrameRev_OK = 1;	// - ĺ˝ĺĺ¸§ćĽć?K														
                        Bsm_FwUpdate.Ack.BinFileFrameNum = offset; // -  ĺ¸§ĺşĺ?
                                
                        // - éćĺ?ĺ¸§ćĽć?
                        if (Bsm_FwInfo.Rev.BinSubSize != ((offset)+1))
                        {
                            uint16_t sendSize = app_bsm_send_data(		DEV_ADDR,       	  			//čŽžĺ¤ĺ°ĺ
                                                                    UNPACK_FW_UPDATE_F0B,  			//ĺ¸§ĺč˝ĺˇ
                                                                    sizeof(Bsm_FwUpdate.Ack),  	//ć°ćŽĺ¤§ĺ°
                                                                    0,                    			//ć°ćŽĺç§ťé?
                                                                    &Bsm_FwUpdate.Ack,         	//ć°ćŽćş?
                                                                    bsm_opt.tx_buff);	  			//ĺéçźĺ˛ĺş
                        }
                                                                            
                        // čżćť¤éĺ¤ćĽćśĺ¸?
                        if( offset_writed && 
                            offset_writed == offset)
                        {
                            rt_kprintf("frame repetition:%d\n",offset);
                        }
                        else
                        {
                            if ((fd = open(BIN_FILE_NAME, O_WRONLY | O_APPEND)) < 0)
                            {
                                rt_kprintf("open %s er.2\r\n", BIN_FILE_NAME);
                            }
                            int size = write(fd, Bsm_FwUpdate.Rev.BinFileFrameData, lenth);							
                            close(fd);
                            
                            if(size > 0)
                                bin_framecnt++;
                            
                            offset_writed = offset;
                        }
                        
                        // - ćĺ?ĺ¸§ćĽćśOK
                        if (Bsm_FwInfo.Rev.BinSubSize == ((offset)+1))
                        {	
                            //LOG_HEX("BIN",16,Bsm_FwUpdate.Rev.BinFileFrameData,512);
                            Bsm_FwUpdate.Ack.Is_AllFrameRev_OK = 1; // - ććĺ¸§ćĽćśOK														
                            // - ćďż??1ĺ¸§ćĽćśOK
                            // ć ĄéŞBINćäťś
                            //----------------------------------------------------------------------------	
                            int crc_ret = app_bin_crc_check(BIN_FILE_NAME);
                            if (crc_ret == 0)
                            {
                                bin_upgrade_step = FW_FILE_END;	
                                Bsm_FwUpdate.Ack.Is_AllFrameCRC_OK = 1;		
                                record_logdata_push(LOG_Inv_FW_CrcOk,0);                                    
                            }
                            else
                            {
                            
                            }

                            
                            uint16_t sendSize = app_bsm_send_data(		DEV_ADDR,       	  			//čŽžĺ¤ĺ°ĺ
                                                                    UNPACK_FW_UPDATE_F0B,  			//ĺ¸§ĺč˝ĺˇ
                                                                    sizeof(Bsm_FwUpdate.Ack),  	//ć°ćŽĺ¤§ĺ°
                                                                    0,                    			//ć°ćŽĺç§ťé?
                                                                    &Bsm_FwUpdate.Ack,         	//ć°ćŽćş?
                                                                    bsm_opt.tx_buff);	  			//ĺéçźĺ˛ĺş
                        }			
                                                                                                                    
                        if(FW_FILE_END == bin_upgrade_step)
                            // - ćďż??1ĺ¸§ćĽćśOK
                            rt_thread_mdelay(1 * RT_TICK_PER_SECOND); 
                    }
                break;
                            
                // - 2.2 ĺ¤ä˝ 
                case FW_FILE_END:
                    {					
                        rt_hw_cpu_reset(); 					
                    }
                break;
                    
            }
        }while(FW_FILE_END ==  bin_upgrade_step);	
    }
    else
    {
        Bsm_FwUpdate.Ack.Is_AllFrameCRC_OK = 0;
        Bsm_FwUpdate.Ack.Is_AllFrameRev_OK = 0;
        Bsm_FwUpdate.Ack.Is_OneFrameRev_OK = 0;
        uint16_t sendSize = app_bsm_send_data( DEV_ADDR,                     //čŽžĺ¤ĺ°ĺ
                                           UNPACK_FW_UPDATE_F0B,           //ĺ¸§ĺč˝ĺˇ
                                           sizeof( Bsm_FwUpdate.Ack ), //ć°ćŽĺ¤§ĺ°
                                           0,                              //ć°ćŽĺç§ťé?
                                           &Bsm_FwUpdate.Ack,          //ć°ćŽćş?
                                           bsm_opt.tx_buff );              //ĺéçźĺ˛ĺş
    }
}

/**
  * @brief F15.čŽžç˝ŽRTCćśé´
  */
void bsm_unpack_set_rtc(void *frame)
{
    BsmFramePackage *pBsmFP = (BsmFramePackage*)frame;
    
    if(pBsmFP->Data_Lenth == 4)
    {
        time_t now;
        rt_device_t device;
        
        now = *(uint32_t *)&pBsmFP->pData[0];
        
        timestamp_sync_rtc(now,5);
        
    }
    else if(pBsmFP->Data_Lenth == 6)
    {
        if((pBsmFP->pData[0] >= 22) && (pBsmFP->pData[1] >= 1) && (pBsmFP->pData[1] <= 12)
            && (pBsmFP->pData[2] >= 1) && (pBsmFP->pData[2] <= 31))
        {
            set_date(2000+pBsmFP->pData[0],pBsmFP->pData[1],pBsmFP->pData[2]);
            set_time(pBsmFP->pData[3],pBsmFP->pData[4],pBsmFP->pData[5]);
        }
    }
}

/**
  * @brief F16.čŽžç˝Žbaud
  */
void bsm_unpack_set_baud(void *frame)
{
    BsmFramePackage *pBsmFP = (BsmFramePackage*)frame;
    uint32_t baud = *(uint32_t*)&pBsmFP->pData[0];
    
    if((baud >= 9600) && (baud <= 921600))
    {
        extern int MX_USART2_UART_Init(uint32_t BaudRate);
        extern uint8_t rs485_ptu_set_baud;
        if(baud != huart2.Init.BaudRate)
        {
            rs485_ptu_set_baud = 1;
            __HAL_UART_DISABLE(&huart2);
            MX_USART2_UART_Init(baud);
        }
        
        *(uint32_t*)&bsm_opt.ack_buff[0] = (uint32_t)huart2.Init.BaudRate;
        uint16_t sendSize = app_bsm_send_data(	pBsmFP->Dev_Addr,   //čŽžďż˝?ďż˝ĺ°ĺ
												pBsmFP->Frame_Fun,  //ĺ¸§ĺč˝ĺˇ
												4, //ć°ćŽĺ¤§ĺ°
												0,                  //ć°ćŽĺç§ťďż??
												bsm_opt.ack_buff,   //ć°ćŽďż??
												bsm_opt.tx_buff);	//ĺéçźĺ˛ĺş	
    }
}

//#define DEBUG_PTU_81

#ifdef DEBUG_PTU_81
BsmFrame81 debug_81 = {0};
#endif
extern uint32_t  HardfaultNormalDelay;
extern int power_on_cnt;
extern uint16_t motor_get_FcApply(void);
#include "MotorInvProtectInclude.h"
extern uint8_t gTestFlystartFlag ;
extern uint8_t gTestFlystartDir ;
extern uint16_t gTestFlystartDstFreq ;
extern uint16_t gTestFlystartDelay ; // 1= 100ms
extern uint16_t gTestFlystartDelay2 ; // 1= 100ms
/**
  * @brief F81.čˇĺĺďż˝?ďż˝ĺ¨çśďż˝? 
  */
 //------------------------------------------------
void bsm_unpack_0x81_maintop(void *frame)
{	
    static uint8_t prev_set = 0;
    time_t now;
	BsmFramePackage *pBsmFP 	= (BsmFramePackage*)frame;
    BsmFrame81_RPDO  *p81_RPDO  = (BsmFrame81_RPDO*)&pBsmFP->pData[0];
    uint16_t u16_data;
    uint32_t u32_data;
    int16_t  s16_data;
    int16_t  s32_data;
    
    rt_memset(bsm_opt.ack_buff,0,BSM_DATA_TX_LENTH);
    
    /* get current time */
    now = time(RT_NULL);
    
    {
        if(pBsmFP->Data_Lenth >= 4)
        {
            timestamp_sync_rtc(p81_RPDO->time_stamp,5);
        }
        
        // data0 offset 24
        // bit0 offset 19 bit 0
        if( p81_RPDO->bit0_valid)
        {
            vfd.is_update_fw = 0;
            
            vfd.manual.start_mask   = p81_RPDO->bit2_ptu_start;
     
            vfd.manual.start        = p81_RPDO->val.bit0;

            vfd.manual.aux_ai1_ctrl_mask =  p81_RPDO->val.bit19;
            mcsdk.CtrlMode               = p81_RPDO->val.bit19 ? mcsdk.CtrlMode : CTRL_MODE_RPM;
            
            vfd.manual.o1_mask      = p81_RPDO->val.bit2;//DO enable
            vfd.manual.o2_mask      = p81_RPDO->val.bit2;//DO enable
            vfd.manual.o1           = p81_RPDO->val.bit7;
            vfd.manual.o2           = p81_RPDO->val.bit4;
            
            mcsdk.tune_11 = p81_RPDO->val.bit15 ? p81_RPDO->val.bit16 : mcsdk.tune_11;
            mcsdk.tune_12 = p81_RPDO->val.bit15 ? p81_RPDO->val.bit17 : mcsdk.tune_12;
            mcsdk.save_tune_param = p81_RPDO->val.bit15 ? p81_RPDO->val.bit18 : mcsdk.save_tune_param;
            
			vfd.driveMotorType  = p81_RPDO->val.bit1 ? 2:vfd.driveMotorType;
            vfd.startDC_SecTick = p81_RPDO->val.bit1 ? p81_RPDO->data[3] : 0;
			vfd.startDC_Cur     = p81_RPDO->val.bit1 ? ((float)p81_RPDO->data[4]/10) : 0;
			
            #ifdef VFD_TEST_DEBUG
            vfd.manual.kmon_mask    = p81_RPDO->val.bit2;//DO enable
      
            vfd.manual.kmon2_mask   = p81_RPDO->val.bit2;//DO enable
            vfd.manual.kmon3_mask   = p81_RPDO->val.bit2;//DO enable

            vfd.manual.kmon    = p81_RPDO->val.bit3;
        
            vfd.manual.kmon2   = p81_RPDO->val.bit5;
            vfd.manual.kmon3   = p81_RPDO->val.bit6;
            
            vfd.manual.tim1_mask = p81_RPDO->val.bit8 & (!p81_RPDO->bit2_ptu_start) & p81_RPDO->val.bit9;//PWM enable
            vfd.manual.tim8_mask = p81_RPDO->val.bit8 & (!p81_RPDO->bit2_ptu_start) & p81_RPDO->val.bit10;//PWM enable
            
            vfd.manual.tim1_u_duty = (vfd.manual.tim1_mask) ? p81_RPDO->data[1]: 0;//PWM duty
            vfd.manual.tim1_v_duty = (vfd.manual.tim1_mask) ? p81_RPDO->data[1]: 0;//PWM duty
            vfd.manual.tim1_w_duty = (vfd.manual.tim1_mask) ? p81_RPDO->data[1]: 0;//PWM duty
            
            vfd.manual.tim8_u_duty = (vfd.manual.tim8_mask) ? p81_RPDO->data[1] : 0;//PWM duty
            vfd.manual.tim8_v_duty = (vfd.manual.tim8_mask) ? p81_RPDO->data[1] : 0;//PWM duty
            vfd.manual.tim8_w_duty = (vfd.manual.tim8_mask) ? p81_RPDO->data[1] : 0;//PWM duty
            
            vfd.manual.start_boost_mask = (p81_RPDO->bit2_ptu_start) ? 0 : p81_RPDO->val.bit12;
            vfd.manual.start_vfd_mask   = (p81_RPDO->bit2_ptu_start) ? 0 : p81_RPDO->val.bit12;
            vfd.manual.start_boost   = p81_RPDO->val.bit14;
            vfd.manual.start_vfd     = p81_RPDO->val.bit13;
            
            vfd.manual.in2_mask = (vfd.manual.start_boost_mask & vfd.manual.start_boost) ? 1:vfd.manual.in2_mask;
            vfd.manual.in2 = (vfd.manual.start_boost_mask & vfd.manual.start_boost) ? 1:vfd.manual.in2;
            
            #endif
           
            
            int32_t freq;
            static int32_t ptu_set_freq = 0;
            static int32_t ptu_set_rpm = 0;
            
            if((p81_RPDO->data[0] != ptu_set_rpm) 
                || (p81_RPDO->data[2] == 0))
            {
                // freq = p81_RPDO->data[0]*100/12;
                freq = p81_RPDO->data[0]*funcCode.code.motorParaM1.elem.ratingFrq/funcCode.code.motorParaM1.elem.ratingSpeed;
                // funcCode.code.motorParaM1.elem.ratingSpeed*freq/funcCode.code.motorParaM1.elem.ratingFrq
                freq = ((freq > 32000)) ? 32000 : freq;
                vfd.ctrl.set_freq  = ((freq >= 0) && (freq <= 36000)) ? freq : vfd.ctrl.set_freq;
            }
            else if((p81_RPDO->data[2] != ptu_set_freq) 
                || (p81_RPDO->data[0] == 0))
            {
                freq = p81_RPDO->data[2];
                freq = ((freq > 32000)) ? 32000 : freq;
                vfd.ctrl.set_freq  = ((freq >= 0) && (freq <= 36000)) ? freq : vfd.ctrl.set_freq;
            }
            
            if(p81_RPDO->val.bit20)
            {
                gTestFlystartFlag = 1;
                vfd.manual.start_mask  = 0;
                gTestFlystartDstFreq = vfd.ctrl.set_freq;
                gTestFlystartDelay   = p81_RPDO->data[5];
                gTestFlystartDelay2  = p81_RPDO->data[6];

                gTestFlystartDir    = p81_RPDO->val.bit21;
            }
            else
            {
                 gTestFlystartFlag = 0;
            }


            ptu_set_rpm = p81_RPDO->data[0];
            ptu_set_freq = p81_RPDO->data[2];
            
            if(vfd.bit.lock_stop) 
               diag_lockstop_reset();

            
            gIgbtBreake.ResetCnter = 0;
        }  

        prev_set = p81_RPDO->bit0_valid;        
    }
    
    #ifdef DEBUG_PTU_81
    if(pSendStream != &debug_81)
    #endif
	{          
        /* XML OFFSET="009" Start */
        toolbox_u32_set_data(&bsm_opt.ack_buff[0] ,1,vfd.ctrl.sys_st);  /* system state */
        toolbox_u32_set_data(&bsm_opt.ack_buff[1] ,1,mcsdk.ControlState); 
        toolbox_u32_set_data(&bsm_opt.ack_buff[2] ,1,vfd.ctrl.boost_st); 
        toolbox_u32_set_data(&bsm_opt.ack_buff[3] ,1,vfd.serial_addr);
        
		toolbox_u32_set_data(&bsm_opt.ack_buff[4] ,2,(INVERTER_IS_RUN ? motor_get_FcApply():0));
		
        
        toolbox_u32_set_data(&bsm_opt.ack_buff[8] ,2,(vfd.ad.aux_ai1));
        toolbox_u32_set_data(&bsm_opt.ack_buff[10] ,2,(uint16_t)vfd.output_rated_power); 
        toolbox_u32_set_data(&bsm_opt.ack_buff[12] ,2,vfd.ad.temp_inv_dcl);
    
        toolbox_u32_set_data(&bsm_opt.ack_buff[16] ,4,rt_tick_get()/RT_TICK_PER_SECOND);   /* power-on time */
        
        toolbox_u32_set_data(&bsm_opt.ack_buff[20] ,4,nvs_datas.accumulator.dataU32[0]);
        toolbox_u32_set_data(&bsm_opt.ack_buff[24] ,4,nvs_datas.accumulator.dataU32[1]);    
        
        toolbox_u32_set_data(&bsm_opt.ack_buff[28] ,4,now);               /* RTC Timestamp */
        toolbox_u32_set_data(&bsm_opt.ack_buff[32] ,2,VFD_SOFT_VERSION); 

        toolbox_u32_set_data(&bsm_opt.ack_buff[34] ,2,vfd.ad.dc_3_3V);     /*   */
        toolbox_u32_set_data(&bsm_opt.ack_buff[36] ,2,vfd.ad.dc_5V);     /*   */
        toolbox_u32_set_data(&bsm_opt.ack_buff[38] ,2,vfd.ad.dc_15V);     /*   */
        
        U32_SET_BIT(bsm_opt.ack_buff[40] ,0,(DIO_NAME_READ_BIT("POW") && (DIO_NAME_READ_DELAY("POW") <= 0)));        
        U32_SET_BIT(bsm_opt.ack_buff[40] ,1,vfd.io.add1);
        U32_SET_BIT(bsm_opt.ack_buff[40] ,2,vfd.io.add2);
        U32_SET_BIT(bsm_opt.ack_buff[40] ,3,vfd.io.kmon);
        U32_SET_BIT(bsm_opt.ack_buff[40] ,4,vfd.io.o1);
        U32_SET_BIT(bsm_opt.ack_buff[40] ,5,(!DIO_NAME_READ_BIT("F_IPM") || (DIO_NAME_READ_DELAY("F_IPM") > 0))); 
        U32_SET_BIT(bsm_opt.ack_buff[40] ,6,vfd.bit.com_485);       
        U32_SET_BIT(bsm_opt.ack_buff[40] ,7,vfd.bit.com_can); 

        U32_SET_BIT(bsm_opt.ack_buff[41] ,0,vfd.io.in1);   
        U32_SET_BIT(bsm_opt.ack_buff[41] ,1,vfd.io.in2);
        U32_SET_BIT(bsm_opt.ack_buff[41] ,2,!vfd.io.led_err);
        U32_SET_BIT(bsm_opt.ack_buff[41] ,3,!vfd.io.led_run);
        
        U32_SET_BIT(bsm_opt.ack_buff[41] ,4,vfd.io.o2);

        U32_SET_BIT(bsm_opt.ack_buff[41] ,7,vfd.bit.mount_fs);

            
        toolbox_u32_set_data(&bsm_opt.ack_buff[42] ,2,vfd.ctrl.set_speed);    
        toolbox_u32_set_data(&bsm_opt.ack_buff[44] ,2,vfd.ctrl.motor_speed);
        
        if(fabs(vfd.acin_freq_folp) > 10)
        {
            toolbox_u32_set_data(&bsm_opt.ack_buff[46] ,2,vfd.ad.acVinR);
            toolbox_u32_set_data(&bsm_opt.ack_buff[48] ,2,vfd.ad.acVinS);
            toolbox_u32_set_data(&bsm_opt.ack_buff[56] ,2,vfd.ad.acVinT);
        }
        else
        {
            toolbox_u32_set_data(&bsm_opt.ack_buff[46] ,2,0);
            toolbox_u32_set_data(&bsm_opt.ack_buff[48] ,2,0);
            toolbox_u32_set_data(&bsm_opt.ack_buff[56] ,2,0);
        }
        toolbox_u32_set_data(&bsm_opt.ack_buff[50] ,2,vfd.ad.vbus_inv);
        toolbox_u32_set_data(&bsm_opt.ack_buff[52] ,2,vfd.ad.ac_iout_u/10);
        toolbox_u32_set_data(&bsm_opt.ack_buff[54] ,2,vfd.ad.ac_iout_v/10);
        
		U32_SET_BIT(bsm_opt.ack_buff[58] ,0,(!DIO_NAME_READ_BIT("OVP_P_BUS")|| (DIO_NAME_READ_DELAY("OVP_P_BUS") > 0))); 
		U32_SET_BIT(bsm_opt.ack_buff[58] ,1,(!DIO_NAME_READ_BIT("F_HD") || (DIO_NAME_READ_DELAY("F_HD") > 0))); 
		U32_SET_BIT(bsm_opt.ack_buff[58] ,2,!DIO_NAME_READ_BIT("CLR_HD")); 
		U32_SET_BIT(bsm_opt.ack_buff[58] ,3,DIO_NAME_READ_BIT("FS_DR")); 
		U32_SET_BIT(bsm_opt.ack_buff[58] ,4,!DIO_NAME_READ_BIT("FS_DR_B")); 
        
        U32_SET_BIT(bsm_opt.ack_buff[58] ,5,(!DIO_NAME_READ_BIT("BREAKIN") || (DIO_NAME_READ_DELAY("BREAKIN") > 0)));

        if(vfd.driveMotorType == 2)
            toolbox_u32_set_data(&bsm_opt.ack_buff[59] ,1,2);
        else
            toolbox_u32_set_data(&bsm_opt.ack_buff[59] ,1,funcCode.code.motorParaM1.elem.motorType); 
        
		toolbox_u32_set_data(&bsm_opt.ack_buff[60] ,1,vfd.voltInputType); 
		toolbox_u32_set_data(&bsm_opt.ack_buff[62] ,2,(int16_t)(vfd.acin_freq_folp*10));
        toolbox_u32_set_data(&bsm_opt.ack_buff[64] ,2,vfd.ad.dcVin);
        toolbox_u32_set_data(&bsm_opt.ack_buff[66] ,2,vfd.ad.temp_igbt); 
        toolbox_u32_set_data(&bsm_opt.ack_buff[68] ,2,vfd.ad.temp_cap);
        
        extern Uint                    gRatio;
        toolbox_u32_set_data(&bsm_opt.ack_buff[70] ,4,gRatio);
        toolbox_u32_set_data(&bsm_opt.ack_buff[74] ,2,vfd.ad.temp_mcu); 
        
        toolbox_u32_set_data(&bsm_opt.ack_buff[76] ,2,vfd.ad.temp_HDC1080);
        toolbox_u32_set_data(&bsm_opt.ack_buff[78] ,2,vfd.ad.moisture_HDC1080);

        if(!INVERTER_IS_RUN)
        {
            toolbox_u32_set_data(&bsm_opt.ack_buff[80] ,2,vfd.ad.acVoutU);
            toolbox_u32_set_data(&bsm_opt.ack_buff[82] ,2,vfd.ad.acVoutV);
            toolbox_u32_set_data(&bsm_opt.ack_buff[84] ,2,vfd.ad.acVoutW);
            toolbox_u32_set_data(&bsm_opt.ack_buff[6] ,2,(int16_t)(vfd.acout_freq_folp*10));
        }
        else
        {
            toolbox_u32_set_data(&bsm_opt.ack_buff[80],2,0);
            toolbox_u32_set_data(&bsm_opt.ack_buff[82],2,0);
            toolbox_u32_set_data(&bsm_opt.ack_buff[84],2,0);
            toolbox_u32_set_data(&bsm_opt.ack_buff[6] ,2,0);
        }
        toolbox_u32_set_data(&bsm_opt.ack_buff[86] ,2,(uint16_t)vfd.filter_ad.ac_vout);
        toolbox_u32_set_data(&bsm_opt.ack_buff[88] ,2,(uint16_t)mcsdk.MotorPower); 
        
        toolbox_u32_set_data(&bsm_opt.ack_buff[90] ,4,(uint32_t)nvs_datas.accumulator.dataF32[0]); 
        toolbox_u32_set_data(&bsm_opt.ack_buff[94] ,4,(uint32_t)vfd.powerconsumption_now); 
        
        toolbox_u32_set_data(&bsm_opt.ack_buff[98] ,4,nvs_datas.accumulator.dataU32[2]);
        toolbox_u32_set_data(&bsm_opt.ack_buff[102] ,4,(uint32_t)nvs_datas.accumulator.dataF32[1]);  
        
        toolbox_u32_set_data(&bsm_opt.ack_buff[106] ,2,(uint32_t)nvs_datas.accumulator.dataU32[3]); 
        toolbox_u32_set_data(&bsm_opt.ack_buff[108] ,1,gPWM.PWMModle);
        toolbox_u32_set_data(&bsm_opt.ack_buff[110] ,1,vfd.board_id); 
        toolbox_u32_set_data(&bsm_opt.ack_buff[111] ,1,vfd.state_code);
        toolbox_u32_set_data(&bsm_opt.ack_buff[112] ,2,vfd.invc_err_code);
        toolbox_u32_set_data(&bsm_opt.ack_buff[114] ,2,vfd.ctrl.motor_freq);
        toolbox_u32_set_data(&bsm_opt.ack_buff[116] ,2,vfd.ad.ac_iout_w/10);
        toolbox_u32_set_data(&bsm_opt.ack_buff[118] ,4,vfd.motor_run_sec);
        toolbox_u32_set_data(&bsm_opt.ack_buff[122] ,4,(uint32_t)vfd.dc_powerconsumption_now);
        toolbox_u32_set_data(&bsm_opt.ack_buff[126] ,2,(uint16_t)vfd.capacity_in);
        rt_memcpy(&bsm_opt.ack_buff[128] ,&vfd.diag,16);   /*  fault code 1 */
        
        toolbox_u32_set_data(&bsm_opt.ack_buff[144] ,4,vfd.spi3_lis3dh.filter_xyz.data.acce.x);
        toolbox_u32_set_data(&bsm_opt.ack_buff[148] ,4,vfd.spi3_lis3dh.filter_xyz.data.acce.y);
        toolbox_u32_set_data(&bsm_opt.ack_buff[152] ,4,vfd.spi3_lis3dh.filter_xyz.data.acce.z);
        
	}	
    
    bsm_com_txcnter.funcid_0x81++;
	uint16_t sendSize = app_bsm_send_data(		pBsmFP->Dev_Addr,   //čŽžďż˝?ďż˝ĺ°ĺ
												pBsmFP->Frame_Fun,  //ĺ¸§ĺč˝ĺˇ
												160,                //ć°ćŽĺ¤§ĺ°
												0,                  //ć°ćŽĺç§ťďż??
												bsm_opt.ack_buff,   //ć°ćŽďż??
												bsm_opt.tx_buff);	//ĺéçźĺ˛ĺş	
}

int nvsdata_read_and_pack(uint8_t *buf)
{
    // read info,config,modbus reg
    if((vfd.bit.nvs_reg_read == 0) && !INVERTER_IS_RUN )
        nvsdata_read_mbreg();
    
    if((vfd.bit.nvs_config_read == 0) && !INVERTER_IS_RUN )
        nvsdata_read_config();
    
    toolbox_u32_set_data( &buf[9],4,nvs_datas.write_paramt_time); 
    toolbox_u32_set_data( &buf[13],1,nvs_datas.config.serial_com_id); 
    toolbox_u32_set_data( &buf[14],4,nvs_datas.config.rs485_baudrate); 
    toolbox_u32_set_data( &buf[18],1,nvs_datas.config.rs485_check_bit); 
    toolbox_u32_set_data( &buf[19],4,nvs_datas.config.can_baudrate); 
    
    toolbox_u32_set_data( &buf[23],1,nvs_datas.config.SetCanAddr.can_x_addr); 
    toolbox_u32_set_data( &buf[24],1,nvs_datas.config.SetCanAddr.can_y_addr); 
    
    toolbox_u32_set_data( &buf[25],2,nvs_datas.modbus.reg_3000[9]); 
    toolbox_u32_set_data( &buf[27],2,nvs_datas.modbus.reg_3000[10]); 
    toolbox_u32_set_data( &buf[29],2,nvs_datas.modbus.reg_3000[12]); 
    toolbox_u32_set_data( &buf[31],2,nvs_datas.modbus.reg_3000[13]); 
    toolbox_u32_set_data( &buf[33],2,nvs_datas.modbus.reg_4000[0]); 
    
    toolbox_u32_set_data( &buf[41],1,0); 
    
    rt_memcpy(&buf[73],&nvs_datas.config.SetAdCa.adca,64);
    
    int size = sizeof(nvs_datas.motor)/2;
    uint16_t *pMotorParam = &nvs_datas.motor.param_src;
    
    for(int i=0;i<(size-1);i++)
    {
        toolbox_u32_set_data( &buf[137+2*i],2,pMotorParam[i]);
    }
    
    
    return 436;
}

void bsm_unpack_0x87_read(void *frame);
void motor_param_update(void);
/**
  * @brief F82.čŽžç˝Žéĺć¨ĄĺĺPFCĺć°ĺ¸§ĺ¸§
  */
void bsm_unpack_0x82_write(void *frame)
{
    static uint32_t last_write_tick = 0;
    
	if(NULL == frame)	return;
	 
    int size = 0;
	BsmFramePackage *pBsmFP = ( BsmFramePackage * )frame;
    uint8_t  *pRecvStream   = ( uint8_t * )pBsmFP->pData-9;
	uint8_t  *pSendStream   = ( uint8_t * )bsm_opt.ack_buff-9;
    
    rt_memset( bsm_opt.ack_buff,0,BSM_DATA_TX_LENTH );
    
    if(!INVERTER_IS_RUN 
        && (rt_tick_get() >= (last_write_tick+RT_TICK_PER_SECOND)))
    {
        /* step 1 ďźset app data */
        nvs_datas.config.serial_com_id = MEM_8(&pRecvStream[13]);
        nvs_datas.config.rs485_baudrate  = MEM_32(&pRecvStream[14]);
        nvs_datas.config.rs485_check_bit = MEM_8(&pRecvStream[18]);
        nvs_datas.config.can_baudrate    = MEM_32(&pRecvStream[19]);
        
        nvs_datas.config.SetCanAddr.can_x_addr = pRecvStream[23];
        nvs_datas.config.SetCanAddr.can_y_addr = pRecvStream[24];
        
        rt_memcpy(&nvs_datas.config.SetAdCa.adca,&pRecvStream[73],64);
        adca_data_fix(&nvs_datas.config.SetAdCa);
        rt_memcpy(vfd.SetAdCa.adca, nvs_datas.config.SetAdCa.adca,sizeof(ad_cal_t));
        
        int size = sizeof(nvs_datas.motor)/2;
        uint16_t *pMotorParam       = &nvs_datas.motor.param_src;
        uint16_t *pMotorParamRecv   = (uint16_t*)&pRecvStream[137];

        if(pBsmFP->Data_Lenth >= 402)
        {
            for(int i=0;i<size;i++)
            {
                if(pMotorParamRecv[i] != pMotorParam[i])
                {
                    pMotorParam[i] = pMotorParamRecv[i];
                    
                }
            }
            nvs_datas.motor.F2_37 = (nvs_datas.motor.F2_37 < 13) ? 13 : nvs_datas.motor.F2_37;
            motor_param_update();
        }
        
        
        /* step 2 write to flash */
        last_write_tick = rt_tick_get();
        nvsdata_write_config();
        
        if((MEM_16(&pRecvStream[25]) >= 1000) && (MEM_16(&pRecvStream[25]) <= (50000)))
            nvsdata_write_one_mbreg("3009",MEM_16(&pRecvStream[25]));
        
        if((MEM_16(&pRecvStream[31]) >= 1000) && (MEM_16(&pRecvStream[31]) <= (50000)))
            nvsdata_write_one_mbreg("300D",MEM_16(&pRecvStream[31]));
        
        if((MEM_16(&pRecvStream[27]) >= 500) && (MEM_16(&pRecvStream[27]) <= (750)))
            nvsdata_write_one_mbreg("300A",MEM_16(&pRecvStream[27]));
        
        nvsdata_write_one_mbreg("300C",MEM_16(&pRecvStream[29]));
        nvsdata_write_one_mbreg("4000",MEM_16(&pRecvStream[33]));
        
        nvsdata_write_motorparam();
        
        nvs_datas.write_paramt_time = time(RT_NULL);
        nvsdata_write_one_mbreg("updateT",nvs_datas.write_paramt_time);
        
    }
	
    pBsmFP->Frame_Fun = 0x82;
    bsm_unpack_0x87_read(frame);
}

/**
  * @brief F87.čˇĺéĺć¨ĄĺĺPFCĺć°ĺ¸§ĺ¸§
  */
void bsm_unpack_0x87_read(void *frame)
{
	if(NULL == frame)	return;
    int size = 0;
	BsmFramePackage *pBsmFP = ( BsmFramePackage * )frame;
	uint8_t  *pRecvStream   = ( uint8_t * )pBsmFP->pData-9;
	uint8_t  *pSendStream   = ( uint8_t * )bsm_opt.ack_buff-9;
	
    rt_memset( bsm_opt.ack_buff,0,BSM_DATA_TX_LENTH );
        
	size = nvsdata_read_and_pack(pSendStream);
        
    if(pBsmFP->Frame_Fun == 0x82)
        bsm_com_txcnter.funcid_0x82++;
    else if(pBsmFP->Frame_Fun == 0x87)
        bsm_com_txcnter.funcid_0x87++;
	uint16_t sendSize = app_bsm_send_data(		pBsmFP->Dev_Addr,	//čŽžďż˝?ďż˝ĺ°ĺ
												pBsmFP->Frame_Fun, 	//ĺ¸§ĺč˝ĺˇ
												size, 				//ć°ćŽĺ¤§ĺ°
												0,                    				//ć°ćŽĺç§ťďż??
												bsm_opt.ack_buff,        				//ć°ćŽďż??
												bsm_opt.tx_buff);	  				//ĺéçźĺ˛ĺş
}

void bsm_unpack_0x22_read(void *frame);
/**
  * @brief F21.čŽžç˝Žĺďż˝?ďż˝ĺ¨
  */
void bsm_unpack_0x21_write(void *frame)
{	
	BsmFramePackage *pBsmFP 	= (BsmFramePackage*)frame;
    uint8_t  *pRecvStream   = (uint8_t *)pBsmFP->pData;
	uint8_t  *pSendStream 	= (uint8_t *)bsm_opt.ack_buff;
    
    if(!INVERTER_IS_RUN )
    {
        if(rt_strlen((char *)&pRecvStream[0])   <= 32)  rt_strncpy((char *)&nvs_datas.info.SerialNo.HwBarCode[0],  (char *)&pRecvStream[0],    32);
        if(rt_strlen((char *)&pRecvStream[32])  <= 32)  rt_strncpy((char *)&nvs_datas.info.SerialNo.HwBarCode[1], (char *)&pRecvStream[32],   32);
        if(rt_strlen((char *)&pRecvStream[64])  <= 32)  rt_strncpy((char *)&nvs_datas.info.SerialNo.HwBarCode[2], (char *)&pRecvStream[64],   32);
        if(rt_strlen((char *)&pRecvStream[96])  <= 32)  rt_strncpy((char *)&nvs_datas.info.SerialNo.HwBarCode[3], (char *)&pRecvStream[96],   32);
        if(rt_strlen((char *)&pRecvStream[128]) <= 32)  rt_strncpy((char *)&nvs_datas.info.SerialNo.HwBarCode[4],(char *)&pRecvStream[128],  32);
        if(rt_strlen((char *)&pRecvStream[160]) <= 32)  rt_strncpy((char *)&nvs_datas.info.SerialNo.HwBarCode[5],(char *)&pRecvStream[160],  32);
        if(rt_strlen((char *)&pRecvStream[192]) <= 32)  rt_strncpy((char *)&nvs_datas.info.SerialNo.HwBarCode[6],(char *)&pRecvStream[192],  32);
        if(rt_strlen((char *)&pRecvStream[224]) <= 32)  rt_strncpy((char *)&nvs_datas.info.SerialNo.HwBarCode[7],(char *)&pRecvStream[224],  32);
        
        rt_memcpy((char *)&nvs_datas.info.SetHwVer.ver[0],(char *)&pRecvStream[256],  32);
        nvsdata_write_info();
    }
    
    pBsmFP->Frame_Fun = 0x21;
    bsm_unpack_0x22_read(frame);
}

/**
  * @brief F22.čˇĺĺďż˝?ďż˝ĺ¨
  */
void bsm_unpack_0x22_read(void *frame)
{	
	BsmFramePackage *pBsmFP 	= (BsmFramePackage*)frame;
    uint8_t  *pRecvStream   = (uint8_t *)pBsmFP->pData;
	uint8_t  *pSendStream 	= (uint8_t *)bsm_opt.ack_buff;
    
    if((!INVERTER_IS_RUN ) && (vfd.bit.nvs_info_read == 0)) 
        // readback NVS data to ask
        nvsdata_read_info();
        
    rt_memcpy( (char *)&pSendStream[0],    (char *)&nvs_datas.info.SerialNo.HwBarCode[0],   32*8);
    rt_memcpy( (char *)&pSendStream[256],    (char *)&nvs_datas.info.SetHwVer,              32);
    
    if(pBsmFP->Frame_Fun == 0x21)
        bsm_com_txcnter.funcid_0x21++;
    else if(pBsmFP->Frame_Fun == 0x22)
        bsm_com_txcnter.funcid_0x22++;
    
    uint16_t sendSize = app_bsm_send_data(		pBsmFP->Dev_Addr,	//čŽžďż˝?ďż˝ĺ°ĺ
												pBsmFP->Frame_Fun,	//ĺ¸§ĺč˝ĺˇ
												288, //ć°ćŽĺ¤§ĺ°
												0,                  //ć°ćŽĺç§ťďż??
												bsm_opt.ack_buff,        //ć°ćŽďż??
												bsm_opt.tx_buff);	//ĺéçźĺ˛ĺş
}

void bsm_unpack_0x84_read(void *frame);
/**
  * @brief F83.čŽžç˝Žĺďż˝?ďż˝ĺ¨
  */
void bsm_unpack_0x83_write(void *frame)
{	
	BsmFramePackage *pBsmFP 	= (BsmFramePackage*)frame;
    uint8_t  *pRecvStream   = (uint8_t *)pBsmFP->pData;
	uint8_t  *pSendStream 	= (uint8_t *)bsm_opt.ack_buff;
    
    if(pRecvStream[0] == 0x5A)
    {
        rt_memset( &nvs_datas.accumulator, 0,   sizeof(nvs_datas.accumulator));
        if(!INVERTER_IS_RUN)
            nvsdata_write_acc();
    }
    
    pBsmFP->Frame_Fun = 0x83;
    bsm_unpack_0x84_read(frame);
}

/**
  * @brief F84.čˇĺĺďż˝?ďż˝ĺ¨
  */
void bsm_unpack_0x84_read(void *frame)
{	
	BsmFramePackage *pBsmFP 	= (BsmFramePackage*)frame;
    uint8_t  *pRecvStream   = (uint8_t *)pBsmFP->pData;
	uint8_t  *pSendStream 	= (uint8_t *)bsm_opt.ack_buff;
    
    toolbox_u32_set_data( &pSendStream[0],4,0); 

    toolbox_u32_set_data( &pSendStream[4],4,(uint32_t)nvs_datas.accumulator.dataF32[0]); 
    toolbox_u32_set_data( &pSendStream[8],4,(uint32_t)nvs_datas.accumulator.dataF32[1]); 
    toolbox_u32_set_data( &pSendStream[12],4,(uint32_t)nvs_datas.accumulator.dataF32[2]); 
    toolbox_u32_set_data( &pSendStream[16],4,(uint32_t)nvs_datas.accumulator.dataF32[3]);

    rt_memcpy(&pSendStream[20],&nvs_datas.accumulator.dataU32, 60*4);
    
    if(pBsmFP->Frame_Fun == 0x84)
        bsm_com_txcnter.funcid_0x84++;
    else if(pBsmFP->Frame_Fun == 0x83)
        bsm_com_txcnter.funcid_0x83++;
    uint16_t sendSize = app_bsm_send_data(		pBsmFP->Dev_Addr,	//čŽžďż˝?ďż˝ĺ°ĺ
												pBsmFP->Frame_Fun,	//ĺ¸§ĺč˝ĺˇ
												260, //ć°ćŽĺ¤§ĺ°
												0,                  //ć°ćŽĺç§ťďż??
												bsm_opt.ack_buff,        //ć°ćŽďż??
												bsm_opt.tx_buff);	//ĺéçźĺ˛ĺş
}


#include <flashdb.h>
extern int log_counts;
extern int fault_counts;
extern struct fdb_tsdb tsdb_log;
extern struct fdb_tsdb tsdb_fault;
extern void fdb_tsl_iter_reverse(fdb_tsdb_t db, fdb_tsl_cb cb, void* arg) ;
extern void fdb_tsl_iter(fdb_tsdb_t db, fdb_tsl_cb cb, void* arg) ;
extern void fdb_tsl_iter_reverse_serial(fdb_tsdb_t db, fdb_tsl_cb cb, void *cb_arg,uint32_t cmd);
static bool tsl_query_cb(fdb_tsl_t tsl, void *arg);
static uint8_t *tsl_copy_buf = 0;


void record_com_db_type_reset(uint8_t db_type)
{
    if((db_type == 1) 
        || (db_type == 2))
    {
        struct fdb_tsdb *ptsdb = NULL;
        
        record_com.db_type = db_type;
    
        if(db_type == 1)
            record_com.ptsdb = &tsdb_fault;
        else if(db_type == 2)
            record_com.ptsdb = &tsdb_log;
        
        record_com.reset_flag = 1;
        record_com.acc_index = 0; 
        record_com.read_index = 0;   
        record_com.cache[0].tsl_time = 0;
        record_com.query_counts = 0;
        fdb_tsl_iter(record_com.ptsdb,tsl_query_cb,"get_end_tsl_time");
        record_com.query_counts = 0;
        fdb_tsl_iter_reverse(record_com.ptsdb,tsl_query_cb,"get_curr_time");
        record_com.counts = record_com.cur_tsl_time - record_com.end_tsl_time + 1;
    }
    
}

static void record_com_reset_acknowledge(uint8_t reset_flag)
{
    if(reset_flag)
    {
        record_com.reset_flag = 0;
        record_com.ctrl.clear_read = 1;
        record_com.acc_index = 0;
    }
}

uint32_t record_get_timestamp(uint8_t *data)
{
    uint32_t timestamp = 0;
    
    struct tm tm_new = {0};
    
    if((data[0] < 22) || (data[1] == 0) || (data[2] == 0))
    {
        return 0;
    }
    else
    {
        /* update date. */
        tm_new.tm_year = data[0]+2000 - 1900;
        tm_new.tm_mon  = data[1] - 1; /* tm_mon: 0~11 */
        tm_new.tm_mday = data[2];
    
        /* update time. */
        tm_new.tm_hour = data[3];
        tm_new.tm_min  = data[4];
        tm_new.tm_sec  = data[5];
    
        /* converts the local time into the calendar time. */
        timestamp = mktime(&tm_new);
    }
    
    return timestamp;
}

static bool tsl_query_cb(fdb_tsl_t tsl, void *arg)
{
    static char *log = NULL;
    struct fdb_blob blob;
    size_t read_len;
    record_log_t *tsldata = NULL;
    uint32_t timestamp = 0;
    
    record_com.query_counts++;
    
    if(log == NULL)
        log = rt_malloc(20*128);
    
    if(log)
    {
        fdb_blob_make(&blob, log, tsl->log_len);
        read_len = fdb_blob_read((fdb_db_t)record_com.ptsdb, fdb_tsl_to_blob(tsl, &blob));
        
        tsldata = ((record_log_t*)(blob.buf)); 
        timestamp = record_get_timestamp((uint8_t *)&tsldata->head.date);
    }
            
    if(!rt_strcmp(arg,"get_curr_time"))
    {
        uint32_t timestamp = record_get_timestamp((uint8_t *)&tsldata->head.date);
                    
        if(record_com.query_counts == 1)
            record_com.cur_tsl_time = tsl->time;
        
        if(log)
        {
            if(timestamp == 0)
            {
                /* unknown time ,donothing*/
                return false;
            }
            else
            {
                record_com.cache[0].tsl_time = tsl->time;
                rt_memcpy(record_com.cur_tsl_date,&(tsldata->head.date),6);
                rt_memcpy(&record_com.cache[0].tsl_log ,tsldata,sizeof(record_log_t));
                return true;
            }
        }
            
        return false;
    }
    else if(!rt_strcmp(arg,"get_end_tsl_time"))
    {
        if(record_com.query_counts == 1)
            record_com.end_tsl_time = tsl->time;
        
        if(log)
        {         
            if(timestamp == 0)
            {
                /* unknown time ,donothing*/
                return false;
            }
            else
            {
                rt_memcpy(record_com.end_tsl_date,&(tsldata->head.date),6);
                return true;
            }
        }

        return false;
    }
    else if(!rt_strcmp(arg,"read_one_tsl"))
    {  
        //
        #if 1
        if(record_com.query_counts % 1000 == 0)
        {
            if(tsl->time > record_com.cur_tsl_time)
            {
                record_com.cur_tsl_time = tsl->time;
                record_com.counts = record_com.cur_tsl_time - record_com.end_tsl_time;
            }
                
            *(uint32_t *)&(bsm_opt.ack_buff[0]) = (record_com.cur_tsl_time - tsl->time)*100/record_com.counts;
            
            BSM_COM_PRINT("tick%d :query %d %%,read %d,acc %d,wait \r\n",rt_tick_get()/2,*(uint32_t *)&(bsm_opt.ack_buff[0])
                                            ,record_com.read_index,record_com.acc_index);
            
            uint16_t sendSize = app_bsm_send_data(	DEV_ADDR,	//čŽžďż˝?ďż˝ĺ°ĺ
												0x09, 	        //ĺ¸§ĺč˝ĺˇ
												4, 			    //ć°ćŽĺ¤§ĺ°
												0,                    				//ć°ćŽĺç§ťďż??
												bsm_opt.ack_buff,        				//ć°ćŽďż??
												bsm_opt.tx_buff);	  				//ĺéçźĺ˛ĺş
            rt_thread_mdelay(100);
            
        }
        #endif
        
        if(tsl->status != FDB_TSL_WRITE)
            return false;
        if(tsl->time == 0)
            return false;
        else if(log)
        {  
            uint8_t cache_tsl_offset = read_len/128;
            
            {
                if(record_com.cache[0].tsl_time == 0)
                {
                    record_com.cache[0].tsl_time = tsl->time;
                    rt_memcpy(&record_com.cache[0].tsl_log ,tsldata,sizeof(record_log_t));
                }    
                
                if(record_com.ctrl.filter_time)
                {
                    uint32_t timestamp = record_get_timestamp((uint8_t *)&tsldata->head.date);
                    
                    if(timestamp == 0)
                    {
                        /* unknown time ,donothing*/
                    }
                    else if(record_com.ctrl.from > record_com.ctrl.to)
                    {
                        if(timestamp > record_com.ctrl.from)
                            return false; 
                        if(timestamp < record_com.ctrl.to)
                            return false; 
                        if(record_com.ctrl.from < record_get_timestamp((uint8_t *)&record_com.end_tsl_date))
                            return true; 
                    }
                    else
                    {
                        if(timestamp > record_com.ctrl.to)
                            return false;
                        if(timestamp < record_com.ctrl.from)
                            return false; 
                        if(record_com.ctrl.to < record_get_timestamp((uint8_t *)&record_com.end_tsl_date))
                            return true; 
                    }
                    
                }
                     

                if(record_com.ctrl.filter_event)
                {        
                    if((record_com.ctrl.filter_event_code == 0xFF)
                        && (tsldata->head.event_code == LOG_Idle))
                        return false;
                    else if((record_com.ctrl.filter_event_code != 0xFF)
                        && (tsldata->head.event_code != record_com.ctrl.filter_event_code))
                        return false; 
                } 
                
                if(record_com.ctrl.filter_diag)
                {       
                    if(tsldata->head.diag_code != record_com.ctrl.filter_diag_code)
                        return false; 
                }
              
                record_com.acc_index++;
            }
//            else
//            {
//                return false;
//            }
            
            uint8_t *data = ((uint8_t*)(blob.buf));
            uint32_t valid_index = record_com.acc_index; 
            
            if(valid_index == record_com.read_index)
            {
                cache_tsl_offset = (cache_tsl_offset > RECORD_cache_tsl_offset) ? RECORD_cache_tsl_offset : cache_tsl_offset;
                
                for(int i = 0 ;i < cache_tsl_offset;i++)
                {
                    rt_memcpy(&record_com.cache[i].tsl_log ,&data[i*128],sizeof(record_log_t));
                    record_com.cache[i].tsl_time = tsl->time;
                }
                
                record_com.cache_tsl_offset = cache_tsl_offset;
                        
                if(tsl_copy_buf)
                {
                    if((record_com.db_type == 0x01) //fault
                     && (record_com.read_offset >=0) 
                     && (record_com.read_offset < cache_tsl_offset))
                        rt_memcpy(tsl_copy_buf,&record_com.cache[record_com.read_offset],sizeof(record_log_t)+4);
                    else
                        rt_memcpy(tsl_copy_buf,&record_com.cache[0],sizeof(record_log_t)+4);
                }
                
                record_com.query_haved_flag = 1;
                record_com.cache_time = tsl->time;
                return true;
            }
            
        }
        
    }
    return false;
}


/**
  * @brief 
  */
void bsm_unpack_0x07(void *frame)
{
	if(NULL == frame)	return;
	BsmFramePackage *pBsmFP = (BsmFramePackage*)frame;
    uint8_t  *pSendStream 	= (uint8_t *)bsm_opt.ack_buff;
    uint32_t tick = rt_tick_get();
    
    if(!vfd.bit.nvs_datas_init)
        return;
    
    if((pBsmFP->pData[0] == 0x1)    // fault db
       || (pBsmFP->pData[0] == 0x2) // log db
    )
    {
        record_com_db_type_reset(pBsmFP->pData[0]);

        pSendStream[0] = pBsmFP->pData[0];
         
        *(uint32_t*)&pSendStream[4] = record_com.counts;  
        *(uint32_t*)&pSendStream[8] = 128;  
        
        if(pBsmFP->pData[0] == 0x1)
        {
            *(uint16_t*)&pSendStream[2] = 20*128; 
            *(uint16_t*)&pSendStream[10] = 20;  
        }
        else if(pBsmFP->pData[0] == 0x2) 
        {
            *(uint16_t*)&pSendStream[2] = 1*128; 
            *(uint16_t*)&pSendStream[10] = 1;  
        }
        
        *(uint32_t*)&pSendStream[12] = record_com.cur_tsl_time;  
        *(uint32_t*)&pSendStream[16] = record_com.end_tsl_time; 
        rt_memcpy(&pSendStream[20],record_com.cur_tsl_date,6); 
        rt_memcpy(&pSendStream[26],record_com.end_tsl_date,6); 
    }

    BSM_COM_PRINT(" 0x07 use tick:%d \r\n",rt_tick_get() - tick);
    BSM_COM_PRINT(" tsdb.cur_tsl_time:%d \r\n",record_com.cur_tsl_time);
    BSM_COM_PRINT(" tsdb.end_tsl_time:%d \r\n",record_com.end_tsl_time);
    BSM_COM_PRINT(" tsdb.total_counts:%d \r\n",record_com.counts);
	
	uint16_t sendSize = app_bsm_send_data(		pBsmFP->Dev_Addr,	//čŽžďż˝?ďż˝ĺ°ĺ
												pBsmFP->Frame_Fun, 	//ĺ¸§ĺč˝ĺˇ
												32, 				//ć°ćŽĺ¤§ĺ°
												0,                    				//ć°ćŽĺç§ťďż??
												bsm_opt.ack_buff,        				//ć°ćŽďż??
												bsm_opt.tx_buff);	  				//ĺéçźĺ˛ĺş
}

/**
  * @brief 
  */
void bsm_unpack_0x08(void *frame)
{
	if(NULL == frame)	return;
	BsmFramePackage *pBsmFP = (BsmFramePackage*)frame;
    uint8_t  *pSendStream 	= (uint8_t *)bsm_opt.ack_buff;
    int ack_size = 0;
    static uint32_t period_tick = 0;
    uint32_t period = 0;
    
    period =  rt_tick_get() - period_tick;
    period_tick = rt_tick_get();
    
    if(!vfd.bit.nvs_datas_init)
        return;
  
    if((record_com.cur_tsl_time == 0) ||
        (record_com.counts == 0)  ||
        (pBsmFP->pData[0] != record_com.db_type))
        record_com_db_type_reset(pBsmFP->pData[0]);
    
    if(pBsmFP->Data_Lenth >= 10)
    {
        *(uint8_t *)&record_com.ctrl = pBsmFP->pData[1];
        record_com.ctrl.clear_read = 0;
        
        record_com.ctrl.filter_event_code = pBsmFP->pData[8];
        record_com.ctrl.filter_diag_code  = pBsmFP->pData[9];
        
        if(pBsmFP->Data_Lenth >= 18)
        {
            #if 1
            record_com.ctrl.from = *(uint32_t *)&pBsmFP->pData[10];
            record_com.ctrl.to = *(uint32_t *)&pBsmFP->pData[14];
            #else
            record_com.ctrl.filter_time = 1;
            record_com.ctrl.from = 1667040950;
            record_com.ctrl.to = 1667041370;//record_com.ctrl.from - 600;
            #endif
        }
    }
    
    if( ((pBsmFP->pData[0] == 0x1)||(pBsmFP->pData[0] == 0x2))
        && (record_com.cur_tsl_time != 0))
    {
        uint32_t tick = rt_tick_get();
        tsl_copy_buf = pSendStream;
        
        record_com.read_index = *(int*)&pBsmFP->pData[4];
        record_com.read_offset = *(uint16_t*)&pBsmFP->pData[2];
        
        record_com.query_haved_flag = 0;
        
        record_com_reset_acknowledge(record_com.reset_flag);
        
        record_com.ctrl.serial_read = 1;
        
        if((record_com.read_index == record_com.acc_index)
            && (record_com.read_offset < record_com.cache_tsl_offset))
        {
            record_com.query_haved_flag = 1;
            
            if(pBsmFP->pData[0] == 0x2)
                rt_memcpy(tsl_copy_buf,&record_com.cache[0],132);
            else
                rt_memcpy(tsl_copy_buf,&record_com.cache[record_com.read_offset],132);
        }    
        else
        {    
            record_com.query_counts = 0;
            
            if(record_com.ctrl.serial_read)
            {
                fdb_tsl_iter_reverse_serial(record_com.ptsdb,tsl_query_cb,"read_one_tsl",record_com.ctrl.clear_read);
            }
            else
            {
                record_com.acc_index = 0;
                fdb_tsl_iter_reverse(record_com.ptsdb,tsl_query_cb,"read_one_tsl");
            }
        }
        if(record_com.ctrl.serial_read)
            BSM_COM_PRINT("[S]");
        if(record_com.ctrl.clear_read)
            BSM_COM_PRINT("[C]");
        if(record_com.ctrl.filter_diag)
            BSM_COM_PRINT("[D%02d]",record_com.ctrl.filter_diag_code);
        
        if(record_com.ctrl.filter_event)
            BSM_COM_PRINT("[E%02d]",record_com.ctrl.filter_event_code);
        
        if(record_com.query_haved_flag)
        {
            ack_size = 132;
            BSM_COM_PRINT("[%d]0x08 usetick[%d],",period,rt_tick_get() - tick);
           
            BSM_COM_PRINT("rd_index[%d],",record_com.read_index);
            BSM_COM_PRINT("rd_offset[%d],",record_com.read_offset);
            
            BSM_COM_PRINT("tslid[%d],%4d-%2d-%2d %02d:%02d:%02d ->",*(uint32_t*)&tsl_copy_buf[0],tsl_copy_buf[4]+2000,tsl_copy_buf[5],tsl_copy_buf[6],
                                                    tsl_copy_buf[7],tsl_copy_buf[8],tsl_copy_buf[9]);
            
            BSM_COM_PRINT("event[%d],diag[%d]  \r\n",tsl_copy_buf[11],tsl_copy_buf[12]);
        }
        else
        {
            BSM_COM_PRINT("[%d]0x08 usetick[%d] ",period,rt_tick_get() - tick);
            BSM_COM_PRINT("rd_index[%d] not find \r\n",record_com.read_index);
            ack_size = 0;
        }
    }
    
    
	uint16_t sendSize = app_bsm_send_data(		pBsmFP->Dev_Addr,	//čŽžďż˝?ďż˝ĺ°ĺ
												pBsmFP->Frame_Fun, 	//ĺ¸§ĺč˝ĺˇ
												ack_size, 			//ć°ćŽĺ¤§ĺ°
												0,                    				//ć°ćŽĺç§ťďż??
												bsm_opt.ack_buff,        				//ć°ćŽďż??
												bsm_opt.tx_buff);	  				//ĺéçźĺ˛ĺş
}

/**
  * @brief 
  */
void bsm_unpack_0x09(void *frame)
{
	if(NULL == frame)	return;
	BsmFramePackage *pBsmFP = (BsmFramePackage*)frame;
    uint8_t  *pSendStream 	= (uint8_t *)bsm_opt.ack_buff;
    int ack_size = 0;

    
    
	uint16_t sendSize = app_bsm_send_data(		pBsmFP->Dev_Addr,	//čŽžďż˝?ďż˝ĺ°ĺ
												pBsmFP->Frame_Fun, 	//ĺ¸§ĺč˝ĺˇ
												ack_size, 			//ć°ćŽĺ¤§ĺ°
												0,                    				//ć°ćŽĺç§ťďż??
												bsm_opt.ack_buff,        				//ć°ćŽďż??
												bsm_opt.tx_buff);	  				//ĺéçźĺ˛ĺş
}


extern int nvs_erase_allchip(void);
/**
  * @brief 
  */
void bsm_unpack_0x17(void *frame)
{
	if(NULL == frame)	return;
	BsmFramePackage *pBsmFP = (BsmFramePackage*)frame;
    uint8_t  *pSendStream 	= (uint8_t *)bsm_opt.ack_buff;
    uint32_t tick = rt_tick_get();
    
    if(!INVERTER_IS_RUN)
        vfd.ctrl.erase_flash = 1;
    
    while(vfd.ctrl.erase_flash == 1)
    {
        vfd.bit.soft_stop = 1;
        
        rt_thread_delay(RT_TICK_PER_SECOND/2);
        *(uint32_t *)&pSendStream[0] = (rt_tick_get() - tick)/2;
        
        BSM_COM_PRINT("0x17 usetick[%d] \r\n",*(uint32_t *)&pSendStream[0]);
        
        uint16_t sendSize = app_bsm_send_data(	pBsmFP->Dev_Addr,	//čŽžďż˝?ďż˝ĺ°ĺ
												pBsmFP->Frame_Fun, 	//ĺ¸§ĺč˝ĺˇ
												4, 				    //ć°ćŽĺ¤§ĺ°
												0,                    				//ć°ćŽĺç§ťďż??
												bsm_opt.ack_buff,        				//ć°ćŽďż??
												bsm_opt.tx_buff);	  				//ĺéçźĺ˛ĺş
        
        
    }
    
    rt_thread_delay(RT_TICK_PER_SECOND/2);
    vfd.bit.soft_stop = 0;
    
    *(uint32_t *)&pSendStream[0] = 0x0;
        
    uint16_t sendSize = app_bsm_send_data(	pBsmFP->Dev_Addr,	//čŽžďż˝?ďż˝ĺ°ĺ
												pBsmFP->Frame_Fun, 	//ĺ¸§ĺč˝ĺˇ
												4, 				    //ć°ćŽĺ¤§ĺ°
												0,                    				//ć°ćŽĺç§ťďż??
												bsm_opt.ack_buff,        				//ć°ćŽďż??
												bsm_opt.tx_buff);	  				//ĺéçźĺ˛ĺş
}

/**
  * @brief app_bsm_com_process 
  */
void app_bsm_com_process(void *p)
{		
	rt_sem_init(&bsm_sem_rev, "bsm_sem_rev", 0, RT_IPC_FLAG_FIFO);
	
	BsmFramePackage *pBsmFP = (BsmFramePackage *) rt_malloc(sizeof(BsmFramePackage));
	
	for(;;)
	{	
		rt_sem_take(&bsm_sem_rev, RT_WAITING_FOREVER); 
        bsm_sem_rev.value = 0;
		rt_memcpy(pBsmFP, &bsm_opt.rx_buff[0] ,9);	
		pBsmFP->pData = &(bsm_opt.rx_buff[9]);	
		
        com_cnter_recv(&com_ptu);
        
		switch(pBsmFP->Frame_Fun)
		{			
			case UNPACK_GET_VER_F01:  
				bsm_unpack_get_version(pBsmFP);		 //F01. čˇĺçćŹďż??
				break;								        
			case UNPACK_GET_DATE_F02:                       
				bsm_unpack_get_date(pBsmFP);		 //F02. čˇĺćśé´ďż??		
				break;				                        
			case UNPACK_GET_DIGITAL_INPUT_F03:              
				bsm_unpack_get_digital_input(pBsmFP);//F03. čˇĺčžĺĽć°ĺ­éĺ¸§
				break;					                    
			case UNPACK_GET_DIGITAL_OUT_F04:                
				bsm_unpack_get_digital_out(pBsmFP);	 //F04. čˇĺčžĺşć°ĺ­éĺ¸§
				break;				                        
			case UNPACK_GET_AD_VALUE_F05:                   
				bsm_unpack_get_adc_value(pBsmFP);	 //F05. čˇĺADć¨Ąćéĺ¸§			
				break;		
            case UNPACK_RESET_MCU_F06:
                bsm_unpack_reset_mcu(pBsmFP);
                break;
            case UNPACK_GET_TOTAL_FAULT_F07:  
				bsm_unpack_0x07(pBsmFP);	         //F07.		
				break;
            case UNPACK_GET_CHOOSE_NUM_F08:  
				bsm_unpack_0x08(pBsmFP);	         //F08.			
				break;
            case UNPACK_GET_CHOOSE_TIME_F09:  
				bsm_unpack_0x09(pBsmFP);		
				break;
							
			case UNPACK_FW_INFO_F0A:   
				bsm_unpack_fw_info(pBsmFP);			//F0A.ĺşäťśäżĄćŻ
				break;					
			case UNPACK_FW_UPDATE_F0B:
                bsm_com_rxcnter.funcid_0x0A++;
				bsm_unpack_fw_update(pBsmFP);		//F0B.ĺşäťść°ćŽďż??
				break;		
            case UNPACK_SET_RTC_F15:
                bsm_unpack_set_rtc(pBsmFP);		    //F15.čŽžç˝Žćśé´
				break;
            case UNPACK_SET_BAUD_F16:
                bsm_unpack_set_baud(pBsmFP);		//F16.čŽžç˝Žbaud
				break;
            case UNPACK_ERASE_F17:
                bsm_unpack_0x17(pBsmFP);		    //F17. erase
				break;
            case UNPACK_SET_BAR_CODE_F21:
                bsm_com_rxcnter.funcid_0x21++;
                bsm_unpack_0x21_write(pBsmFP);		//F21.
				break;
            case UNPACK_GET_BAR_CODE_F22:
                bsm_com_rxcnter.funcid_0x22++;
                bsm_unpack_0x22_read(pBsmFP);		//F22.
                break;
			case UNPACK_GET_INV_BOOST_STATE_F81:
                bsm_com_rxcnter.funcid_0x81++;
                if(rt_tick_get() >= RT_TICK_PER_SECOND*2)
                    bsm_unpack_0x81_maintop(pBsmFP);		//F81.čˇĺéĺĺĺĺĺďż??
				break;	
			case UNPACK_SET_INV_BOOST_PARAM_F82:	
                bsm_com_rxcnter.funcid_0x82++;
				bsm_unpack_0x82_write(pBsmFP);//F82.čŽžç˝Žéĺĺĺĺĺďż??
				break;	
            case UNPACK_GET_INV_BOOST_PARAM_F87:	
                bsm_com_rxcnter.funcid_0x87++;
				bsm_unpack_0x87_read(pBsmFP); //F87.čˇĺéĺĺĺĺĺďż??
				break;		
            case UNPACK_SET_NVS_CONFIG_F83:	
                bsm_com_rxcnter.funcid_0x83++;
				bsm_unpack_0x83_write(pBsmFP);		//F83.
				break;
            case UNPACK_GET_NVS_CONFIG_F84:	
                bsm_com_rxcnter.funcid_0x84++;
				bsm_unpack_0x84_read(pBsmFP);		//F84.
				break;
					
			default:
				break;
		}
		
	}
	rt_free(pBsmFP);
}

/**
  * @brief create bsm com task 
  */
int app_bsm_com_thread_create(void)
{
    rt_thread_t tid;
 
    //! startup a thread
    tid = rt_thread_create("bsm_com", app_bsm_com_process, RT_NULL, 4096, 13, 20);
    if (tid != RT_NULL)
        rt_thread_startup(tid);

    return 0;
}
INIT_APP_EXPORT(app_bsm_com_thread_create);


void cmd_bsm_com(int argc, char **argv)
{
    if(argc >= 3)
    {
        if(!rt_strcmp("info",argv[1]))
        {
            if((!rt_strcmp("log",argv[2]))
                ||(!rt_strcmp("fault",argv[2])))
            {
                uint8_t type = 0x1;
                
                if(!rt_strcmp("log",argv[2]))
                    type = 0x2;
                else if(!rt_strcmp("fault",argv[2]))
                    type = 0x1;
                
                BsmFramePackage *pBsmFP = (BsmFramePackage *)&bsm_opt.rx_buff[0];
                uint8_t *pdata = &(bsm_opt.rx_buff[9]);
                pBsmFP->Frame_Fun = 0x7;
                pBsmFP->Data_Lenth = 2;
                pdata[0] = type;
				
                rt_sem_release(&bsm_sem_rev);
            }
        }
        else if((!rt_strcmp("read",argv[1])) ||
            (!rt_strcmp("serial_read",argv[1])) ||
            (!rt_strcmp("clear_read",argv[1])))
        {
            uint8_t type = 0x1;
            uint32_t start = 1;
            uint32_t num = 1;
            uint8_t  offset_num = 1;
            
            if(!rt_strcmp("log",argv[2]))
                    type = 0x2;
                else if(!rt_strcmp("fault",argv[2]))
                    type = 0x1;
            
            if(argc >= 4)
                start = atoi(argv[3]);
            
            if(argc >= 5)
                num   = atoi(argv[4]);    
            
            if(argc >= 6)
                offset_num = atoi(argv[5]);
            offset_num = (offset_num > 20) ? 20 : offset_num;
            
            for(int i =0;i < num;i++)
            {
                BsmFramePackage *pBsmFP = (BsmFramePackage *)&bsm_opt.rx_buff[0];
                uint8_t *pdata = &(bsm_opt.rx_buff[9]);
                pBsmFP->Frame_Fun = 0x8;
                pBsmFP->Data_Lenth = 18;
                pdata[0] = type;
                
                if(!rt_strcmp("read",argv[1]))
                    pdata[1] = 0;
                else if(!rt_strcmp("serial_read",argv[1]))
                    pdata[1] = 0x02;
                else if(!rt_strcmp("clear_read",argv[1]))
                    pdata[1] = 0x01;
                
                if(i == 0)
                    pdata[1] |= 0x01;
                
                for(int j =0;j < offset_num;j++)
                {
                    *(uint16_t *)&pdata[2] = j;
                    *(uint32_t *)&pdata[4] = start;
                
                    rt_sem_release(&bsm_sem_rev);
                
                    rt_thread_mdelay(100);
                }
                
                start++;
            }
        }
        else if( (!rt_strcmp("event_read",argv[1]))
            || (!rt_strcmp("diag_read",argv[1])))
        {
            uint8_t type = 0x1;
            uint32_t start = 1;
            uint8_t filter_code = 0xFF;
            uint32_t num = 1;
            
            if(!rt_strcmp("log",argv[2]))
                    type = 0x2;
                else if(!rt_strcmp("fault",argv[2]))
                    type = 0x1;
            
            if(argc >= 4)
            {
                filter_code = atoi(argv[3]);
            }
            
            if(argc >= 5)
                num   = atoi(argv[4]);    
            
            for(int i =0;i < num;i++)
            {
                BsmFramePackage *pBsmFP = (BsmFramePackage *)&bsm_opt.rx_buff[0];
                uint8_t *pdata = &(bsm_opt.rx_buff[9]);
                pBsmFP->Frame_Fun = 0x8;
                pBsmFP->Data_Lenth = 18;
                pdata[0] = type;
                
                if(!rt_strcmp("event_read",argv[1]))
                    pdata[1] = 0x06;
                else if(!rt_strcmp("diag_read",argv[1]))
                    pdata[1] = 0x0A;
 
                if(i == 0)
                    pdata[1] |= 0x01;
                
                *(uint16_t *)&pdata[2] = 0;
                *(uint32_t *)&pdata[4] = start++;
                
                pdata[8] = filter_code;
                pdata[9] = filter_code;
                
                rt_sem_release(&bsm_sem_rev);
                
                rt_thread_mdelay(100);
            }
        }
    }
}
MSH_CMD_EXPORT_ALIAS(cmd_bsm_com,bsm_com, cmd_bsm_com set);

