/* USER CODE BEGIN Header */
/**
  ******************************************************************************
  * @file           : main.c
  * @brief          : Main program body
  ******************************************************************************
  * @attention
  *
  * <h2><center>&copy; Copyright (c) 2021 STMicroelectronics.
  * All rights reserved.</center></h2>
  *
  * This software component is licensed by ST under BSD 3-Clause license,
  * the "License"; You may not use this file except in compliance with the
  * License. You may obtain a copy of the License at:
  *                        opensource.org/licenses/BSD-3-Clause
  *
  ******************************************************************************
  */
/* USER CODE END Header */
/* Includes ------------------------------------------------------------------*/
#include "main.h"
#include "uapp.h"
#include "user_mcsdk_task.h"
#include "MotorInclude.h"
#define DBG_TAG "mcsdk"
#define DBG_LVL DBG_LOG
#include <rtdbg.h>
extern int16_t motor_get_vbus_fast(void);
extern void motor_set_freqref(int16_t freq);
extern int16_t motor_err_occurred(void);
extern int16_t motor_reset_err_occurred(void);
extern int16_t motor_get_rpmout(void);
extern int16_t motor_get_poles(void);
extern int16_t motor_get_freqout(void);
extern void motor_runcmd_set(uint16_t cmd);
extern uint16_t motor_runcmd_get(void);
extern void motor_set_torch(uint16_t val);
extern int16_t motor_get_freqoutRef(void);
extern void motor_set_tune(uint16_t val);
extern uint32_t motor_get_errorcode(void);
extern uint16_t motor_get_tune(void);
extern uint16_t CurFinalU, CurFinalV, CurFinalW, VolFinalUV, VolFinalUW, VolFinalVW;
extern void motor_param_update(void);
extern int16_t motor_rpm2frq(int16_t rpm);
/* USER CODE BEGIN PFP */

/* USER CODE END PFP */
int mc_main_init(void);
static rt_err_t mcsdk_init(void);
static rt_err_t mcsdk_start(void);
static rt_err_t mcsdk_stop(void);
static rt_err_t mcsdk_control(int cmd, void *arg);
static rt_err_t mcsdk_machine_task(void);
void mcsdk_init_drive_paramts(uint8_t motor_index);

static const struct rt_mcsdk_ops ops =
{
    .init   = mcsdk_init,
    .start  = mcsdk_start,
    .stop   = mcsdk_stop,
    .machine_task = mcsdk_machine_task,
    .control = mcsdk_control,
};


rt_mcsdk_t    mcsdk =
{
    .ops = &ops,
};


static rt_err_t mcsdk_init(void)
{
    mc_main_init();
    //mcsdk.CtrlMode = 1;

    vfd.ctrl.set_freq  = nvs_datas.modbus.reg_3000[0x09];

    mcsdk.CtrlMode  = CTRL_MODE_RPM;
    mcsdk.init_flag = 1;
    return 0;
}

static rt_err_t mcsdk_start(void)
{
    if (!mcsdk.init_flag)
        mcsdk.ops->init();

    mcsdk.cmd_start =  1;
    return 0;
}

static rt_err_t mcsdk_stop(void)
{
    if (!mcsdk.init_flag)
        mcsdk.ops->init();

    mcsdk.cmd_start =  0;
    return 0;
}

static rt_err_t mcsdk_control(int cmd, void *arg)
{
    if (!mcsdk.init_flag)
        mcsdk.ops->init();

    switch (cmd)
    {
    default:
        break;
    }

    return 0;
}

int vfd_get_tim1_breakflag(void)
{
    uint8_t ipm_irq_flag =  0;

    if ((mcsdk.error_code != 0)  ||
            (mcsdk.init_flag && (!DIO_READ_BIT(F_HD_PIN) || (DIO_IRQ_DELAY(F_HD_PIN) > 0)))
       )
        ipm_irq_flag = 1;
    else
        ipm_irq_flag = 0;

    if (ipm_irq_flag && (mcsdk.error_code != mcsdk.error_code_save))
    {
        mcsdk.err_cnt++;
        mcsdk.error_code_save = mcsdk.error_code;
        LOG_I("  mcdriver: %d ", mcsdk.error_code);
    }

    return ipm_irq_flag;
}


void motor_save_tuneparam(void)
{
    uint16_t motor_crc = 0xFFFF;

    vfd.bit.nvs_reg_read = 1;
    nvs_datas.motor.param_src = NVS_MOTOR_FUNCCODE;
    motor_param_update();

    if (mcsdk.save_tune_param)
    {
        mcsdk.save_tune_param = 0;
        nvs_datas.motor.param_src = NVS_MOTOR_USER;
        nvsdata_write_motorparam();
    }
}

void motor_funcCode2nvsdata(FUNCCODE_ALL *pfunc)
{
    nvs_datas.motor.motorType               = pfunc->code.motorParaM1.elem.motorType       ;
    nvs_datas.motor.ratingPower             = pfunc->code.motorParaM1.elem.ratingPower     ;
    nvs_datas.motor.ratingVoltage           = pfunc->code.motorParaM1.elem.ratingVoltage   ;
    nvs_datas.motor.ratingCurrent           = pfunc->code.motorParaM1.elem.ratingCurrent   ;
    nvs_datas.motor.ratingFrq               = pfunc->code.motorParaM1.elem.ratingFrq       ;
    nvs_datas.motor.ratingSpeed             = pfunc->code.motorParaM1.elem.ratingSpeed     ;
    nvs_datas.motor.runDir                  = pfunc->code.runDir                           ;
    nvs_datas.motor.pmsmRs                  = pfunc->code.motorParaM1.elem.pmsmRs          ;
    nvs_datas.motor.pmsmLd                  = pfunc->code.motorParaM1.elem.pmsmLd          ;
    nvs_datas.motor.pmsmLq                  = pfunc->code.motorParaM1.elem.pmsmLq          ;
    nvs_datas.motor.pmsmCoeff               = pfunc->code.motorParaM1.elem.pmsmCoeff       ;
    nvs_datas.motor.maxFrq                  = pfunc->code.maxFrq                         ;
    nvs_datas.motor.upperFrq                = pfunc->code.upperFrq                         ;
    nvs_datas.motor.lowerFrq                = pfunc->code.lowerFrq                         ;
    nvs_datas.motor.carrierFrq              = pfunc->code.carrierFrq;
    nvs_datas.motor.accTime1                = pfunc->code.accTime1  ;
    nvs_datas.motor.decTime1                = pfunc->code.decTime1  ;
    nvs_datas.motor.motorCtrlMode           = pfunc->code.motorCtrlMode  ;

    rt_memcpy(&nvs_datas.motor.vcSpdLoopKp1, &pfunc->group.f2, sizeof(funcCode.group.f2));
    rt_memcpy(&nvs_datas.motor.F3_00,       &pfunc->group.f3, 34 * 2);
    rt_memcpy(&nvs_datas.motor.F6_00,       &pfunc->group.f6, 23 * 2);
    rt_memcpy(&nvs_datas.motor.F9_00,       &pfunc->group.f9, 9 * 2);
    nvs_datas.motor.F9_13                  = pfunc->group.f9[13];
    nvs_datas.motor.F9_48                  = pfunc->group.f9[48];
}


void motor_nvsdata2funcCode(void)
{
    funcCode.code.motorParaM1.elem.motorType        = nvs_datas.motor.motorType               ;
    funcCode.code.motorParaM1.elem.ratingPower      = nvs_datas.motor.ratingPower             ;
    funcCode.code.motorParaM1.elem.ratingVoltage    = nvs_datas.motor.ratingVoltage           ;
    funcCode.code.motorParaM1.elem.ratingCurrent    = nvs_datas.motor.ratingCurrent           ;
    funcCode.code.maxFrq                            = nvs_datas.motor.maxFrq                  ;
    funcCode.code.motorParaM1.elem.ratingFrq        = nvs_datas.motor.ratingFrq               ;
    funcCode.code.motorParaM1.elem.ratingSpeed      = nvs_datas.motor.ratingSpeed             ;
    funcCode.code.runDir                            = nvs_datas.motor.runDir                  ;
    funcCode.code.motorParaM1.elem.pmsmRs           = nvs_datas.motor.pmsmRs                  ;
    funcCode.code.motorParaM1.elem.pmsmLd           = nvs_datas.motor.pmsmLd                  ;
    funcCode.code.motorParaM1.elem.pmsmLq           = nvs_datas.motor.pmsmLq                  ;
    funcCode.code.motorParaM1.elem.pmsmCoeff        = nvs_datas.motor.pmsmCoeff               ;
    funcCode.code.upperFrq                          = nvs_datas.motor.upperFrq                ;
    funcCode.code.lowerFrq                          = nvs_datas.motor.lowerFrq                ;
    funcCode.code.carrierFrq                        = nvs_datas.motor.carrierFrq              ;
    funcCode.code.accTime1                          = nvs_datas.motor.accTime1                ;
    funcCode.code.decTime1                          = nvs_datas.motor.decTime1                ;
    funcCode.code.motorCtrlMode                     = nvs_datas.motor.motorCtrlMode           ;

    rt_memcpy(&funcCode.group.f2, &nvs_datas.motor.vcSpdLoopKp1, sizeof(funcCode.group.f2));
    rt_memcpy(&funcCode.group.f3, &nvs_datas.motor.F3_00, 34 * 2);
    rt_memcpy(&funcCode.group.f6, &nvs_datas.motor.F6_00, 23 * 2);
    rt_memcpy(&funcCode.group.f9, &nvs_datas.motor.F9_00, 9 * 2);
    funcCode.group.f9[13] = nvs_datas.motor.F9_13;
    funcCode.group.f9[48] = nvs_datas.motor.F9_48;
}


void motor_param_update(void)
{
    static uint8_t first_init = 1;
    static uint16_t motor_crc = 0xFFFF;

    if (nvs_datas.motor.param_src == NVS_MOTOR_USER)
    {
        // user define in nvs_datas.motor
        nvs_datas.motor.ratingCurrent       = (float)nvs_datas.motor.ratingPower * 82.5f / (float)nvs_datas.motor.ratingVoltage;
    }
    else if ((nvs_datas.motor.param_src == NVS_MOTOR_TYC90_LD_YS90_1L)
             || (nvs_datas.motor.param_src == NVS_MOTOR_TYC90_LD_YS90_1R))
    {
        motor_funcCode2nvsdata((FUNCCODE_ALL *)&funcCodeDefault);

        nvs_datas.motor.motorType               = 0x1;
        nvs_datas.motor.ratingPower             = 1100;
        nvs_datas.motor.ratingVoltage           = 380;
        nvs_datas.motor.ratingCurrent           = (float)nvs_datas.motor.ratingPower * 82.5f / (float)nvs_datas.motor.ratingVoltage;
        nvs_datas.motor.ratingFrq               = 12500;
        nvs_datas.motor.ratingSpeed             = 1500;
        nvs_datas.motor.runDir                  = 0;
        nvs_datas.motor.pmsmRs                  = 2951;
        nvs_datas.motor.pmsmLd                  = 2225;
        nvs_datas.motor.pmsmLq                  = 2473;
        nvs_datas.motor.pmsmCoeff               = 2976;
        nvs_datas.motor.spdCtrlDriveTorqueLimit = 580;
        nvs_datas.motor.upperFrq                = 13335; // 1= 0.01hz
        nvs_datas.motor.lowerFrq                = 0;
        nvs_datas.motor.weakFlusCoef            = 0;
        nvs_datas.motor.vcSpdLoopKp1            = 35;
        nvs_datas.motor.vcSpdLoopTi1            = 25;
        nvs_datas.motor.vcSpdLoopChgFrq1        = 500;
        nvs_datas.motor.vcSpdLoopKp2            = 20;
        nvs_datas.motor.vcSpdLoopTi2            = 100;
        nvs_datas.motor.vcSpdLoopChgFrq2        = 1000;
        nvs_datas.motor.carrierFrq              = 80;
        nvs_datas.motor.accTime1                = 50;
        nvs_datas.motor.decTime1                = 100;
        nvs_datas.motor.motorCtrlMode           = 0;// 1=ACIM-VF 0=PMSM-SVC
        nvs_datas.motor.stopMode                = FUNCCODE_stopMode_FREESTOP;
        nvs_datas.motor.mAcrKp                  = 3296;
        nvs_datas.motor.mAcrKi                  = 699;
        nvs_datas.motor.tAcrKp                  = 3296;
        nvs_datas.motor.tAcrKi                  = 699;
        nvs_datas.motor.F2_07                   = 28;   // 
        nvs_datas.motor.F2_12                   = 850;   // 
        nvs_datas.motor.F2_25                   = 1; //disable init position
        nvs_datas.motor.F2_36                   = 60;   //
        nvs_datas.motor.F2_37                   = 50;   // min Fcq
        nvs_datas.motor.F6_05                   = 50;   // % start DC cur
        nvs_datas.motor.F6_06                   = 0;    // .1s start DC sec
        nvs_datas.motor.F2_24                   = 120;  // ParEst max cur
        nvs_datas.motor.F2_41                   = 80;   // ParEst min cur
        nvs_datas.motor.BreakinAutomaticOutput  = 1;

    }
    else if (nvs_datas.motor.param_src == NVS_MOTOR_TYC80_LS_YS80_5)
    {
        motor_funcCode2nvsdata((FUNCCODE_ALL *)&funcCodeDefault);

        nvs_datas.motor.motorType               = 0x1;
        nvs_datas.motor.ratingPower             = 1100;
        nvs_datas.motor.ratingVoltage           = 380;
        nvs_datas.motor.ratingCurrent           = (float)nvs_datas.motor.ratingPower * 82.5f / (float)nvs_datas.motor.ratingVoltage;
        nvs_datas.motor.ratingFrq               = 12500;
        nvs_datas.motor.ratingSpeed             = 1500;
        nvs_datas.motor.runDir                  = 0;
        nvs_datas.motor.pmsmRs                  = 4930;
        nvs_datas.motor.pmsmLd                  = 3859;
        nvs_datas.motor.pmsmLq                  = 4410;
        nvs_datas.motor.pmsmCoeff               = 2498;
        nvs_datas.motor.spdCtrlDriveTorqueLimit = 400;
        nvs_datas.motor.upperFrq                = 36000; // 1= 0.01hz
        nvs_datas.motor.lowerFrq                = 0;
        nvs_datas.motor.weakFlusCoef            = 0;
        nvs_datas.motor.vcSpdLoopKp1            = 20;
        nvs_datas.motor.vcSpdLoopTi1            = 50;
        nvs_datas.motor.vcSpdLoopChgFrq1        = 500;
        nvs_datas.motor.vcSpdLoopKp2            = 20;
        nvs_datas.motor.vcSpdLoopTi2            = 100;
        nvs_datas.motor.vcSpdLoopChgFrq2        = 1000;
        nvs_datas.motor.carrierFrq              = 80;
        nvs_datas.motor.accTime1                = 50;
        nvs_datas.motor.decTime1                = 100;
        nvs_datas.motor.motorCtrlMode           = 0;// 1=ACIM-VF 0=PMSM-SVC
        nvs_datas.motor.stopMode                = FUNCCODE_stopMode_FREESTOP;
        nvs_datas.motor.mAcrKp                  = 5175;
        nvs_datas.motor.mAcrKi                  = 1176;
        nvs_datas.motor.tAcrKp                  = 5175;
        nvs_datas.motor.tAcrKi                  = 1176;
        nvs_datas.motor.F2_25                   = 1; //disable init position
        nvs_datas.motor.F2_36                   = 60;   //
        nvs_datas.motor.F2_37                   = 50; // min Fcq
        nvs_datas.motor.F6_05                   = 50; //% start DC cur
        nvs_datas.motor.F6_06                   = 0; //.1s start DC sec
        nvs_datas.motor.F2_24                   = 41;// ParEst max cur
        nvs_datas.motor.F2_41                   = 25;// ParEst min cur
        nvs_datas.motor.BreakinAutomaticOutput  = 1;
    }
    else if (nvs_datas.motor.param_src == NVS_MOTOR_ACIM_VF)
    {
        motor_funcCode2nvsdata((FUNCCODE_ALL *)&funcCodeDefault);

        nvs_datas.motor.motorType               = 0; // 1=PMSM 0=ACIM
        nvs_datas.motor.ratingPower             = 7000;
        nvs_datas.motor.ratingVoltage           = 380;
        nvs_datas.motor.ratingCurrent           = (float)nvs_datas.motor.ratingPower * 82.5f / (float)nvs_datas.motor.ratingVoltage;
        nvs_datas.motor.ratingFrq               = 5000;
        nvs_datas.motor.ratingSpeed             = 600;
        nvs_datas.motor.runDir                  = 0;
        nvs_datas.motor.pmsmRs                  = 4930;
        nvs_datas.motor.pmsmLd                  = 3859;
        nvs_datas.motor.pmsmLq                  = 4410;
        nvs_datas.motor.pmsmCoeff               = 2498;
        nvs_datas.motor.spdCtrlDriveTorqueLimit = 1570;
        nvs_datas.motor.upperFrq                = 36000; // 1= 0.01hz
        nvs_datas.motor.lowerFrq                = 0;
        nvs_datas.motor.weakFlusCoef            = 0;
        nvs_datas.motor.vcSpdLoopKp1            = 20;
        nvs_datas.motor.vcSpdLoopTi1            = 50;
        nvs_datas.motor.vcSpdLoopChgFrq1        = 500;
        nvs_datas.motor.vcSpdLoopKp2            = 20;
        nvs_datas.motor.vcSpdLoopTi2            = 100;
        nvs_datas.motor.vcSpdLoopChgFrq2        = 1000;
        nvs_datas.motor.carrierFrq              = 80;
        nvs_datas.motor.accTime1                = 50;
        nvs_datas.motor.decTime1                = 100;
        nvs_datas.motor.motorCtrlMode           = 1;// 1=ACIM-VF 0=PMSM-SVC
        nvs_datas.motor.stopMode                = FUNCCODE_stopMode_FREESTOP;
        nvs_datas.motor.mAcrKp                  = 5175;
        nvs_datas.motor.mAcrKi                  = 1176;
        nvs_datas.motor.tAcrKp                  = 5175;
        nvs_datas.motor.tAcrKi                  = 1176;
        nvs_datas.motor.F2_25                   = 1; //disable init position
        nvs_datas.motor.F2_36                   = 60;   //
        nvs_datas.motor.F2_37                   = 50; // min Fcq
        nvs_datas.motor.F6_05                   = 0; //% start DC cur
        nvs_datas.motor.F6_06                   = 0; //.1s start DC sec
        nvs_datas.motor.F2_24                   = 41;// ParEst max cur
        nvs_datas.motor.F2_41                   = 25;// ParEst min cur
        nvs_datas.motor.F3_00                   = 1;
        nvs_datas.motor.F3_03                   = 0;
        nvs_datas.motor.F3_04                   = 0;
        nvs_datas.motor.F3_05                   = 3000;
        nvs_datas.motor.F3_06                   = 200;
        nvs_datas.motor.F3_07                   = 5000;
        nvs_datas.motor.F3_08                   = 380;
        nvs_datas.motor.BreakinAutomaticOutput  = 0;

    }
    else if (nvs_datas.motor.param_src == NVS_MOTOR_AXIAL_FAN)
    {
        motor_funcCode2nvsdata((FUNCCODE_ALL *)&funcCodeDefault);

        nvs_datas.motor.motorType               = 0x1;// 1=PMSM 0=ACIM
        nvs_datas.motor.ratingPower             = 1100;
        nvs_datas.motor.ratingVoltage           = 380;
        nvs_datas.motor.ratingCurrent           = (float)nvs_datas.motor.ratingPower * 82.5f / (float)nvs_datas.motor.ratingVoltage;
        nvs_datas.motor.ratingFrq               = 12500;
        nvs_datas.motor.ratingSpeed             = 1500;
        nvs_datas.motor.runDir                  = 0;
        nvs_datas.motor.pmsmRs                  = 3122;
        nvs_datas.motor.pmsmLd                  = 2585;
        nvs_datas.motor.pmsmLq                  = 4267;
        nvs_datas.motor.pmsmCoeff               = 3025;
        nvs_datas.motor.spdCtrlDriveTorqueLimit = 580;
        nvs_datas.motor.upperFrq                = 13335; // 1= 0.01hz  133.33Hz=1600RPM
        nvs_datas.motor.lowerFrq                = 0;
        nvs_datas.motor.weakFlusCoef            = 0;
        nvs_datas.motor.vcSpdLoopKp1            = 35;
        nvs_datas.motor.vcSpdLoopTi1            = 25;
        nvs_datas.motor.vcSpdLoopChgFrq1        = 500;
        nvs_datas.motor.vcSpdLoopKp2            = 20;
        nvs_datas.motor.vcSpdLoopTi2            = 100;
        nvs_datas.motor.vcSpdLoopChgFrq2        = 1000;
        nvs_datas.motor.carrierFrq              = 80;
        nvs_datas.motor.accTime1                = 50;
        nvs_datas.motor.decTime1                = 200;
        nvs_datas.motor.motorCtrlMode           = 0;// 1=ACIM-VF 0=PMSM-SVC
        nvs_datas.motor.stopMode                = FUNCCODE_stopMode_FREESTOP;
        nvs_datas.motor.mAcrKp                  = 5232;
        nvs_datas.motor.mAcrKi                  = 1022;
        nvs_datas.motor.tAcrKp                  = 5232;
        nvs_datas.motor.tAcrKi                  = 1022;
        nvs_datas.motor.F2_07                   = 28;   // 
        nvs_datas.motor.F2_12                   = 850;   // 
        nvs_datas.motor.F2_25                   = 1; //disable init position
        nvs_datas.motor.F2_36                   = 60;   //
        nvs_datas.motor.F2_37                   = 50;   // min Fcq
        nvs_datas.motor.F6_05                   = 50;   // % start DC cur
        nvs_datas.motor.F6_06                   = 0;    // .1s start DC sec
        nvs_datas.motor.F2_24                   = 120;  // ParEst max cur
        nvs_datas.motor.F2_41                   = 80;   // ParEst min cur
        nvs_datas.motor.BreakinAutomaticOutput  = 1;
    }
    else if (nvs_datas.motor.param_src == NVS_MOTOR_ZRVH108)
    {
        motor_funcCode2nvsdata((FUNCCODE_ALL *)&funcCodeDefault);

        nvs_datas.motor.motorType               = 0x1;// 1=PMSM 0=ACIM
        nvs_datas.motor.ratingPower             = 12000;
        nvs_datas.motor.ratingVoltage           = 380;
        nvs_datas.motor.ratingCurrent           = (float)nvs_datas.motor.ratingPower * 82.5f / (float)nvs_datas.motor.ratingVoltage;
        nvs_datas.motor.ratingFrq               = 24000;
        nvs_datas.motor.ratingSpeed             = 4800;
        nvs_datas.motor.runDir                  = 0;
        nvs_datas.motor.pmsmRs                  = 333;
        nvs_datas.motor.pmsmLd                  = 417;
        nvs_datas.motor.pmsmLq                  = 547;
        nvs_datas.motor.pmsmCoeff               = 3233;
        nvs_datas.motor.spdCtrlDriveTorqueLimit = 1570;
        nvs_datas.motor.upperFrq                = 36000; // 1= 0.01hz
        nvs_datas.motor.lowerFrq                = 0;
        nvs_datas.motor.weakFlusCoef            = 0;
        nvs_datas.motor.vcSpdLoopKp1            = 20;
        nvs_datas.motor.vcSpdLoopTi1            = 50;
        nvs_datas.motor.vcSpdLoopChgFrq1        = 500;
        nvs_datas.motor.vcSpdLoopKp2            = 20;
        nvs_datas.motor.vcSpdLoopTi2            = 100;
        nvs_datas.motor.vcSpdLoopChgFrq2        = 1000;
        nvs_datas.motor.carrierFrq              = 80;
        nvs_datas.motor.accTime1                = 100;
        nvs_datas.motor.decTime1                = 100;
        nvs_datas.motor.motorCtrlMode           = 0;// 1=ACIM-VF 0=PMSM-SVC
        nvs_datas.motor.stopMode                = FUNCCODE_stopMode_FREESTOP;
        nvs_datas.motor.mAcrKp                  = 4735;
        nvs_datas.motor.mAcrKi                  = 610;
        nvs_datas.motor.tAcrKp                  = 4735;
        nvs_datas.motor.tAcrKi                  = 610;
        nvs_datas.motor.F2_25                   = 1; //disable init position
        nvs_datas.motor.F2_36                   = 50;   //
        nvs_datas.motor.F2_37                   = 20; // min Fcq
        nvs_datas.motor.F6_05                   = 0; //% start DC cur
        nvs_datas.motor.F6_06                   = 0; //.1s start DC sec
        nvs_datas.motor.F2_24                   = 80;// ParEst max cur
        nvs_datas.motor.F2_41                   = 50;// ParEst min cur
        nvs_datas.motor.BreakinAutomaticOutput  = 1;
    }
    else if (nvs_datas.motor.param_src == NVS_MOTOR_TPRS)
    {
        motor_funcCode2nvsdata((FUNCCODE_ALL *)&funcCodeDefault);

        nvs_datas.motor.motorType               = 0x1;// 1=PMSM 0=ACIM
        nvs_datas.motor.ratingPower             = 12000;
        nvs_datas.motor.ratingVoltage           = 380;
        nvs_datas.motor.ratingCurrent           = (float)nvs_datas.motor.ratingPower * 82.5f / (float)nvs_datas.motor.ratingVoltage;

        nvs_datas.motor.ratingFrq               = 12000;
        nvs_datas.motor.ratingSpeed             = 3600;
        nvs_datas.motor.runDir                  = 0;
        nvs_datas.motor.pmsmRs                  = 560;
        nvs_datas.motor.pmsmLd                  = 550;
        nvs_datas.motor.pmsmLq                  = 1494;
        nvs_datas.motor.pmsmCoeff               = 2647;
        nvs_datas.motor.spdCtrlDriveTorqueLimit = 1500;
        nvs_datas.motor.upperFrq                = 36000; // 1= 0.01hz
        nvs_datas.motor.lowerFrq                = 0;
        nvs_datas.motor.weakFlusCoef            = 0;
        nvs_datas.motor.vcSpdLoopKp1            = 20;
        nvs_datas.motor.vcSpdLoopTi1            = 50;
        nvs_datas.motor.vcSpdLoopChgFrq1        = 500;
        nvs_datas.motor.vcSpdLoopKp2            = 20;
        nvs_datas.motor.vcSpdLoopTi2            = 100;
        nvs_datas.motor.vcSpdLoopChgFrq2        = 1000;
        nvs_datas.motor.carrierFrq              = 80;
        nvs_datas.motor.accTime1                = 100;
        nvs_datas.motor.decTime1                = 100;
        nvs_datas.motor.motorCtrlMode           = 0;// 1=ACIM-VF 0=PMSM-SVC
        nvs_datas.motor.stopMode                = FUNCCODE_stopMode_FREESTOP;
        nvs_datas.motor.mAcrKp                  = 6353;
        nvs_datas.motor.mAcrKi                  = 1026;
        nvs_datas.motor.tAcrKp                  = 6353;
        nvs_datas.motor.tAcrKi                  = 1026;
        nvs_datas.motor.F2_25                   = 1; //disable init position
        nvs_datas.motor.F2_36                   = 50;   //
        nvs_datas.motor.F2_37                   = 20; // min Fcq
        nvs_datas.motor.F6_05                   = 0;  //% start DC cur
        nvs_datas.motor.F6_06                   = 0;  //.1s start DC sec
        nvs_datas.motor.F2_24                   = 80; // ParEst max cur
        nvs_datas.motor.F2_41                   = 50; // ParEst min cur
        nvs_datas.motor.BreakinAutomaticOutput  = 0;
    }
    else
    {
        //default,do nothing
        nvs_datas.motor.param_src = NVS_MOTOR_FUNCCODE;
    }

    if (nvs_datas.motor.param_src == NVS_MOTOR_FUNCCODE)
        motor_funcCode2nvsdata(&funcCode);
    else
    {
        motor_nvsdata2funcCode();
    }

    motor_crc = nvs_datas.motor.crc;
}
extern uint8_t gTestFlystartFlag;
u16 PwrOnDelay = 0; //wujian ĺąč˝ä¸çľ1Sĺçćé
static  rt_err_t mcsdk_machine_task(void)
{
    static uint32_t syscnt = 0;
    static SYS_StateTypeDef last_state;
    static uint32_t tErrResetDelay = 0;

    if (!mcsdk.init_flag)
        mcsdk.ops->init();
    mcsdk.error_code  = motor_err_occurred();
    vfd.invc_err_code = mcsdk.error_code;
    mcsdk.torque_max  = (INVERTER_IS_RUN) ? gAsr.Asr.MaxFlag : 0;

    uint8_t comm_timeout_flag = (rt_tick_get() >= 10 * RT_TICK_PER_SECOND)   &&
                                (com_can.timeout_cnt >= 500)            &&
                                (com_modbus.timeout_cnt >= 500)        &&
                                (com_ptu.timeout_cnt >= 500)           &&
                                (com_485.timeout_cnt >= 500);


    if (!com_can.flag_normal        &&
            !com_modbus.flag_normal     &&
            !com_485.flag_normal        &&
            !com_ptu.flag_normal        &&
            !gTestFlystartFlag
       )
    {
        mcsdk.CtrlMode = CTRL_MODE_RPM;

        vfd.ctrl.set_freq  = nvs_datas.modbus.reg_3000[0x09];
    }

    vfd.ctrl.set_freq = (vfd.ctrl.set_freq < 0) ? 0 : vfd.ctrl.set_freq;
    vfd.ctrl.set_freq = (vfd.ctrl.set_freq > 36000) ? 0 : vfd.ctrl.set_freq;

    mcsdk.MotorSpeed        =  motor_get_rpmout();
    mcsdk.MotorSpeedRefNow  = (mcsdk.tune_11 || mcsdk.tune_12) ? 0 : motor_get_rpmoutRef();
    mcsdk.Motorfreq         =  motor_get_freqout();
    mcsdk.ControlSpeed      = vfd.ctrl.set_speed;
    mcsdk.Temperature       = gTemperature.Temp;
    static int32_t save_freq_ref = 0;
    float out = 0;
    float err = 0;
    float iout = (vfd.fast_ad.ac_iout_u + vfd.fast_ad.ac_iout_v + vfd.fast_ad.ac_iout_w) / 3;

    err = vfd.PIdcin.Ref - iout;

    if (INVERTER_IS_RUN)
        pid_control(&vfd.PIdcin, err);
    else
        pid_reinit(&vfd.PIdcin);


    out = vfd.PIdcin.Out;
    out = (vfd.PPowerdcin.Out > out) ? out : vfd.PPowerdcin.Out;

    if (out == 0)
    {
        mcsdk.ControlFreq  = vfd.ctrl.set_freq;
        save_freq_ref = motor_get_freqoutRef();
    }
    else
    {
        float set_freq;

        set_freq = save_freq_ref;

        if ((set_freq * (1 + out)) < vfd.ctrl.set_freq)
            mcsdk.ControlFreq = set_freq * (1 + out);
        else
            mcsdk.ControlFreq = vfd.ctrl.set_freq;
    }

    vfd.startDC_SecTick = (vfd.startDC_SecTick > 3600) ? 3600 : vfd.startDC_SecTick;
    vfd.startDC_Cur = (vfd.startDC_Cur > ((float)nvs_datas.motor.ratingCurrent / 100)) ? ((float)nvs_datas.motor.ratingCurrent / 100) : vfd.startDC_Cur;

    //?????????????????80%
    int FreqOverTempLimit = funcCode.code.motorParaM1.elem.ratingFrq * 4 / 5 ; //80%

    if (vfd.ctrl.load_reducing &&
            (mcsdk.ControlFreq > FreqOverTempLimit)
       )
        mcsdk.ControlFreq = FreqOverTempLimit;

    motor_set_freqref(mcsdk.ControlFreq); //

    if (nvs_datas.motor.param_src == NVS_MOTOR_FUNCCODE)
        motor_funcCode2nvsdata(&funcCode);

    // if (vfd_get_tim1_breakflag())
    // if (vfd_get_tim1_breakflag()||motor_get_errorcode())
    if (vfd_get_tim1_breakflag() || (motor_get_errorcode()) && (++PwrOnDelay > 100)) //wujian ĺąč˝ĺźćşĺ1S
    {
        tErrResetDelay = 0;
        //ćé
        PwrOnDelay = 100;

        if (VFD_INPUT_24V
                || VFD_INPUT_NULL
                || !VFD_ENABLE
                || (VFD_INPUT_AC && (vfd.acin_absfreq < 40))
                || (VFD_INPUT_DC && (vfd.fast_ad.dcVin < 400))
                || (ERROR_LOW_UDC == motor_get_errorcode()))
        {
            if ((ERROR_LOW_UDC == motor_get_errorcode()) && INVERTER_IS_RUN) vfd.bit.novolt_warning_flag = 1;
            mcsdk.ControlState = ST_STOP;
        }
        else
            mcsdk.ControlState = ST_ERROR;
    }
    else
        tErrResetDelay++;

    if (last_state != mcsdk.ControlState)
    {
        static uint8_t first  = 1;

        if (!first)
            record_logdata_push(LOG_InvSt, mcsdk.ControlState);

        first = 0;
        last_state = mcsdk.ControlState;
        syscnt = 0;
        LOG_I("  state:%d BK%d ", mcsdk.ControlState, hardware_irq_pin.f_tim1_breakin_flag);
    }

    vfd.io.rev_overrpm_flag = (((vfd.acout_freq_folp < (motor_rpm2frq(-450)/100)) && funcCode.code.runDir == 0)
                            ||  ((vfd.acout_freq_folp > (motor_rpm2frq(450)/100)) && funcCode.code.runDir == 1));
    switch (mcsdk.ControlState)
    {
    case ST_IDLE:
        mcsdk.ControlState = ST_STOP;
    case ST_STOP:
        mcsdk.MCworktime_cnt = 0;

        if (INVERTER_IS_RUN)
            motor_runcmd_set(SCI_RUN_CMD_FREE_STOP);

        if (motor_get_errorcode() && ((syscnt + 1) % 500 == 0) && (rt_tick_get() > 10 * RT_TICK_PER_SECOND))
        {
            vfd.invc_err_code = 0;
            LL_TIM_ClearFlag_BRK2(TIM1);
            LL_TIM_ClearFlag_BRK(TIM1);
            hardware_irq_pin.f_tim1_breakin_flag = 0;
            motor_reset_err_occurred();
        }

        if (mcsdk.cmd_start
                && ((mcsdk.ControlFreq != 0) || (mcsdk.tune_11 || mcsdk.tune_12) || (vfd.driveMotorType == 2))
                && (motor_get_errorcode() == 0)
                && (tErrResetDelay >= 200)
                && !vfd.io.rev_overrpm_flag
                )
        {
            __HAL_TIM_CLEAR_FLAG(&htim1, TIM_FLAG_BREAK);
            __HAL_TIM_CLEAR_FLAG(&htim1, TIM_FLAG_BREAK2);

            if (gMainStatus.RunStep != STATUS_STOP)
                motor_runcmd_set(SCI_RUN_CMD_FREE_STOP);
            else
                mcsdk.ControlState = ST_START;
        }


        break;

    case ST_START:

        mcsdk.ControlState = ST_SOFTUP;

        if (mcsdk.tune_11 || mcsdk.tune_12)
        {
            if (mcsdk.tune_11)   motor_set_tune(11);
            else motor_set_tune(12);
            mcsdk.ControlState = ST_RUN;
        }

        motor_runcmd_set(SCI_RUN_CMD_FWD_RUN);

        if (!mcsdk.cmd_start
                || ((mcsdk.ControlFreq == 0) && (mcsdk.tune_12 == 0) && (mcsdk.tune_11 == 0) && ((vfd.driveMotorType != 2)))
                    )
        {
            motor_runcmd_set(SCI_RUN_CMD_FREE_STOP);
            mcsdk.ControlState = ST_STOP;
        }

        break;

    case ST_SOFTUP:
    case ST_RUN:
        mcsdk.MCworktime_cnt++;
        mcsdk.MCstoptime_cnt = 0;
        if ((mcsdk.Motorfreq >= (mcsdk.ControlFreq * 0.85f)) || (syscnt >= 500))
        {
            mcsdk.ControlState = ST_RUN;
        }

        if ((motor_get_tune() == 0) &&
                (mcsdk.tune_11 || mcsdk.tune_12))
        {
            mcsdk.tune_11 = 0;
            mcsdk.tune_12 = 0;
            vfd.manual.start = 0;
            vfd.manual.start_vfd = 0;
            motor_save_tuneparam();
            vfd.manual.start_vfd = 0;
            vfd.manual.start = 0;
        }

        if (!mcsdk.cmd_start
                || ((mcsdk.ControlFreq == 0) && (mcsdk.tune_12 == 0) && (mcsdk.tune_11 == 0))
           )
        {
            if ((vfd.ctrl.sys_st == ST_ERROR)
                    || (ST_ERROR_LOCK == vfd.ctrl.sys_st)
                    || (mcsdk.MotorSpeed <= 100)
                    || (nvs_datas.motor.stopMode == FUNCCODE_stopMode_FREESTOP)
                    || (mcsdk.tune_11 || mcsdk.tune_12))
                motor_runcmd_set(SCI_RUN_CMD_FREE_STOP);
            else
                motor_runcmd_set(SCI_RUN_CMD_STOP);

            if (!INVERTER_IS_RUN)
            {
                mcsdk.tune_11 = 0;
                mcsdk.tune_12 = 0;
                mcsdk.ControlState = ST_STOP;

            }
        }
        else if((!INVERTER_IS_RUN || (RUN_STATUS_STOP == runStatus))
            && mcsdk.cmd_start && (mcsdk.ControlFreq != 0) && (mcsdk.tune_12 == 0) && (mcsdk.tune_11 == 0))
             mcsdk.ControlState = ST_START;

        if ((vfd.driveMotorType == 2) && ((mcsdk.MCworktime_cnt / 100) >= vfd.startDC_SecTick))
        {
            vfd.manual.start = 0;
            vfd.manual.start_vfd = 0;
            motor_runcmd_set(SCI_RUN_CMD_FREE_STOP);
        }
        break;

    case ST_ERROR:
        mcsdk.MCworktime_cnt = 0;

        if (motor_runcmd_get() != SCI_RUN_CMD_RESET_ERROR)
            motor_runcmd_set(SCI_RUN_CMD_FREE_STOP);

        if ((syscnt + 1) % 500 == 0)
        {
            vfd.invc_err_code = 0;
            LL_TIM_ClearFlag_BRK2(TIM1);
            LL_TIM_ClearFlag_BRK(TIM1);
            hardware_irq_pin.f_tim1_breakin_flag = 0;
            motor_reset_err_occurred();
        }

        if (!vfd_get_tim1_breakflag() && !motor_get_errorcode() && (syscnt >= 200))
            mcsdk.ControlState = ST_STOP;

        break;

    default:

        mcsdk.ControlState = ST_STOP;

        break;
    }

    if(mcsdk.ControlState != ST_RUN)
        mcsdk.MCstoptime_cnt++;
    
    syscnt++;


    return 0;
}

void mcsdk_set_torque(uint16_t val) // 1= 0.01A
{
    val = (float)(val) * 100 / 803;
    motor_set_torch(val);
}
/* Private includes ----------------------------------------------------------*/
/* USER CODE BEGIN Includes */
#include "IQMathLib.h"
#include "arm_math.h"
#include "testApp.h"
#include "GlobalIncludes.h"
#include "DeviceInit.h"
#include "SystemDefine.h"
#include "MotorDefine.h"
#include "SubPrgInclude.h"
#include "bsp_FDCAN.h"
#include "f_comm.h"
/* USER CODE END Includes */

/* Private typedef -----------------------------------------------------------*/
/* USER CODE BEGIN PTD */

/* USER CODE END PTD */

/* Private define ------------------------------------------------------------*/
/* USER CODE BEGIN PD */
/* USER CODE END PD */

/* Private macro -------------------------------------------------------------*/
/* USER CODE BEGIN PM */

/* USER CODE END PM */

/* Private variables ---------------------------------------------------------*/
extern ADC_HandleTypeDef hadc1;
extern ADC_HandleTypeDef hadc2;
extern DMA_HandleTypeDef hdma_adc1;
extern DMA_HandleTypeDef hdma_adc2;


TIM_HandleTypeDef htim1;
TIM_HandleTypeDef htim4;

/* USER CODE BEGIN PV */
int16_t gTest;
/* USER CODE END PV */

/* Private function prototypes -----------------------------------------------*/
void SystemClock_Config(void);
static void MX_GPIO_Init(void);
static void MX_DMA_Init(void);
static void MX_ADC1_Init(void);
static void MX_ADC2_Init(void);
static void MX_TIM1_Init(void);
static void MX_TIM4_Init(void);
static void MX_USART1_UART_Init(void);

static Uint m_CntTime = 0, m_BaseTime = 0;
static Uint m_LoopFlag = 2;
static Uint m_Counter = 0;
/**
  * @brief  The application entry point.
  * @retval int
  */
int mc_main_init(void)
{
    /* USER CODE BEGIN Init */
    __set_PRIMASK(1); // Disable interrupt
    /* USER CODE END Init */

    /* Configure the system clock */
//  SystemClock_Config();

    /* USER CODE BEGIN SysInit */
    SCB->CCR &= ~SCB_CCR_DIV_0_TRP_Msk;
    /* USER CODE END SysInit */

    /* Initialize all configured peripherals */
    MX_GPIO_Init();
    MX_DMA_Init();
    MX_ADC1_Init();
    MX_ADC2_Init();
    MX_TIM1_Init();
    MX_TIM4_Init();
    InitPeripherals();
    InitForMotorApp();
    InitForFunctionApp();
    RunTask();
    __set_PRIMASK(0); // Enable interrupt

    m_LoopFlag = 2;
    m_BaseTime = GetTime(htim4);
    /********ĺ ä¸şç§ťć?äşéŠąĺ¨ĺ¨ĺçľćşčżč˝˝äżć¤ĺč˝ďźč?ĽĺéĺçćŹ500EäšćŻĺ¨čżč˝˝äżć¤ĺ˝ć°ć´ć°ĺźďźćäťĽć?¤ĺ?ĺŻäťĽĺ é? */
    // gLineCur.MaxCurLimit = 1500;//1000;//622;//wujian modify percent of rate curr

    return 0;
}

extern AC_Phase_T  Iac_u, Iac_v, Iac_w;
int16 OutVoltageTemp0;
int16 OutVoltageTemp;
int mc_main_05ms_loopback(void)
{
    if (!mcsdk.init_flag)
        return -1;

#ifdef VFD_TEST_DEBUG
    if (vfd.manual.tim1_mask)
        return -1;
#endif

    m_CntTime = m_BaseTime - GetTime(htim4); // Gets the current count value
    //C_TIME_05MS
    {
        m_LoopFlag ++;

        if (m_CntTime >= C_TIME_05MS)
            m_BaseTime -= C_TIME_05MS;

        Main05msMotor(); // speed measurement

        mcsdk.BusVoltage = (float)motor_get_vbus_fast() * 0.1f;

        OutVoltageTemp += ((gVoltUVW.Uout >> 5) - (OutVoltageTemp >> 5));
        mcsdk.OutVoltage = INVERTER_IS_RUN ? (OutVoltageTemp / 10) : 0;

        {
            vfd.fast_ad.ac_iout_u = CurFinalU * 0.01f *  vfd.SetAdCa.adca[5] * 0.001f;
            vfd.fast_ad.ac_iout_v = CurFinalV * 0.01f *  vfd.SetAdCa.adca[6] * 0.001f;
            vfd.fast_ad.ac_iout_w = CurFinalW * 0.01f *  vfd.SetAdCa.adca[7] * 0.001f;
        }

        if ((m_LoopFlag & 0x03) == 0) // prA
        {
            Main05msFunctionA();
            Main05msFunctionB();
            m_Counter++;
        }
        else if ((m_LoopFlag & 0x03) == 1) // prB
        {
            Main05msFunctionC();
        }
        else if ((m_LoopFlag & 0x03) == 2) // prC
        {
            Main05msFunctionD();
            Main2msMotorA();
        }
        else if ((m_LoopFlag & 0x03) == 3) // prD
        {
            Main2msMotorB();
            Main2msMotorC();
            Main2msMotorD();
            //            gExcursionInfo.EnableCount = 200; //čˇłčżéśćź
        }

        m_CntTime = __IQsat(m_CntTime, 65500, C_TIME_05MS);
    } //end of if(m_CntTime >= C_TIME_05MS)
    //    Main0msFunction();

    mcsdk.MCState = GetRunstatus();
    return 0;
}


/**
  * @brief ADC1 Initialization Function
  * @param None
  * @retval None
  */
static void MX_ADC1_Init(void)
{

    /* USER CODE BEGIN ADC1_Init 0 */

    /* USER CODE END ADC1_Init 0 */

    ADC_MultiModeTypeDef multimode = {0};
    ADC_ChannelConfTypeDef sConfig = {0};

    /* USER CODE BEGIN ADC1_Init 1 */
    /**ADC1 GPIO Configuration
    PC1     ------> ADC12_IN7    Vbus
    PC2     ------> ADC12_IN8    IU
    PA0     ------> ADC12_IN1    INV_CAP_T
       */

    /* USER CODE END ADC1_Init 1 */
    /** Common config
    */
    hadc1.Instance = ADC1;
    hadc1.Init.ClockPrescaler = ADC_CLOCK_SYNC_PCLK_DIV4;
    hadc1.Init.Resolution = ADC_RESOLUTION_12B;
    hadc1.Init.DataAlign = ADC_DATAALIGN_RIGHT;
    hadc1.Init.GainCompensation = 0;
    hadc1.Init.ScanConvMode = ADC_SCAN_ENABLE;
    hadc1.Init.EOCSelection = ADC_EOC_SEQ_CONV;
    hadc1.Init.LowPowerAutoWait = DISABLE;
    hadc1.Init.ContinuousConvMode = DISABLE;
    hadc1.Init.NbrOfConversion = 4;
    hadc1.Init.DiscontinuousConvMode = DISABLE;
    hadc1.Init.ExternalTrigConv = ADC_EXTERNALTRIG_T1_TRGO;
    hadc1.Init.ExternalTrigConvEdge = ADC_EXTERNALTRIGCONVEDGE_RISINGFALLING;
    hadc1.Init.DMAContinuousRequests = ENABLE;
    hadc1.Init.Overrun = ADC_OVR_DATA_OVERWRITTEN;
    hadc1.Init.OversamplingMode = DISABLE;
    if (HAL_ADC_Init(&hadc1) != HAL_OK)
    {
        Error_Handler();
    }
    /** Configure the ADC multi-mode
    */
    multimode.Mode = ADC_DUALMODE_REGSIMULT;
    multimode.DMAAccessMode = ADC_DMAACCESSMODE_12_10_BITS;
    multimode.TwoSamplingDelay = ADC_TWOSAMPLINGDELAY_1CYCLE;
    if (HAL_ADCEx_MultiModeConfigChannel(&hadc1, &multimode) != HAL_OK)
    {
        Error_Handler();
    }
    /** Configure Regular Channel
    */
    sConfig.Channel = ADC_CHANNEL_6;// IW
    sConfig.Rank = ADC_REGULAR_RANK_1;
    sConfig.SamplingTime = ADC_SAMPLETIME_2CYCLES_5;
    sConfig.SingleDiff = ADC_SINGLE_ENDED;
    sConfig.OffsetNumber = ADC_OFFSET_NONE;
    sConfig.Offset = 0;
    if (HAL_ADC_ConfigChannel(&hadc1, &sConfig) != HAL_OK)
    {
        Error_Handler();
    }
    /** Configure Regular Channel
    */
    sConfig.Channel = ADC_CHANNEL_7;//VBUS
    sConfig.Rank = ADC_REGULAR_RANK_2;
    if (HAL_ADC_ConfigChannel(&hadc1, &sConfig) != HAL_OK)
    {
        Error_Handler();
    }

    /** Configure Regular Channel
    */
    sConfig.Channel = ADC_CHANNEL_8;//IU
    sConfig.Rank = ADC_REGULAR_RANK_3;
    if (HAL_ADC_ConfigChannel(&hadc1, &sConfig) != HAL_OK)
    {
        Error_Handler();
    }

    /** Configure Regular Channel
    */
    sConfig.Channel = ADC_CHANNEL_9;//IV
    sConfig.Rank = ADC_REGULAR_RANK_4;
    if (HAL_ADC_ConfigChannel(&hadc1, &sConfig) != HAL_OK)
    {
        Error_Handler();
    }

    /* USER CODE END ADC1_Init 2 */
    /* Run the ADC calibration in single-ended mode */
    if (HAL_ADCEx_Calibration_Start(&hadc1, ADC_SINGLE_ENDED) != HAL_OK)
    {
        /* Calibration Error */
        Error_Handler();
    }
}

/**
  * @brief ADC2 Initialization Function
  * @param None
  * @retval None
  */
static void MX_ADC2_Init(void)
{

    /* USER CODE BEGIN ADC2_Init 0 */

    /* USER CODE END ADC2_Init 0 */

    ADC_ChannelConfTypeDef sConfig = {0};

    /**ADC2 GPIO Configuration
    PC3     ------> ADC12_IN9    IV
    PC4     ------> ADC2_IN5     AUX_AI1+
    PC5     ------> ADC2_IN11    Tem
    */
    /** Common config
    */
    hadc2.Instance = ADC2;
    hadc2.Init.ClockPrescaler = ADC_CLOCK_SYNC_PCLK_DIV4;
    hadc2.Init.Resolution = ADC_RESOLUTION_12B;
    hadc2.Init.DataAlign = ADC_DATAALIGN_RIGHT;
    hadc2.Init.GainCompensation = 0;
    hadc2.Init.ScanConvMode = ADC_SCAN_ENABLE;
    hadc2.Init.EOCSelection = ADC_EOC_SEQ_CONV;
    hadc2.Init.LowPowerAutoWait = DISABLE;
    hadc2.Init.ContinuousConvMode = DISABLE;
    hadc2.Init.NbrOfConversion = 4;
    hadc2.Init.DiscontinuousConvMode = DISABLE;
    hadc2.Init.DMAContinuousRequests = DISABLE;
    hadc2.Init.Overrun = ADC_OVR_DATA_OVERWRITTEN;
    hadc2.Init.OversamplingMode = DISABLE;
    if (HAL_ADC_Init(&hadc2) != HAL_OK)
    {
        Error_Handler();
    }
    /** Configure Regular Channel
    */
    sConfig.Channel = ADC_CHANNEL_5;//AUX_AI1+
    sConfig.Rank = ADC_REGULAR_RANK_1;
    sConfig.SamplingTime = ADC_SAMPLETIME_2CYCLES_5;
    sConfig.SingleDiff = ADC_SINGLE_ENDED;
    sConfig.OffsetNumber = ADC_OFFSET_NONE;
    sConfig.Offset = 0;
    if (HAL_ADC_ConfigChannel(&hadc2, &sConfig) != HAL_OK)
    {
        Error_Handler();
    }
    /** Configure Regular Channel
    */
    sConfig.Channel = ADC_CHANNEL_11;//IGBT_T
    sConfig.Rank = ADC_REGULAR_RANK_2;
    if (HAL_ADC_ConfigChannel(&hadc2, &sConfig) != HAL_OK)
    {
        Error_Handler();
    }

    /** Configure Regular Channel
    */
    sConfig.Channel = ADC_CHANNEL_3;//ACinR
    sConfig.Rank = ADC_REGULAR_RANK_3;
    if (HAL_ADC_ConfigChannel(&hadc2, &sConfig) != HAL_OK)
    {
        Error_Handler();
    }

    /** Configure Regular Channel
    */
    sConfig.Channel = ADC_CHANNEL_4;//ACinS
    sConfig.Rank = ADC_REGULAR_RANK_4;
    if (HAL_ADC_ConfigChannel(&hadc2, &sConfig) != HAL_OK)
    {
        Error_Handler();
    }

    /* Run the ADC calibration in single-ended mode */
    if (HAL_ADCEx_Calibration_Start(&hadc2, ADC_SINGLE_ENDED) != HAL_OK)
    {
        /* Calibration Error */
        Error_Handler();
    }

    /* USER CODE BEGIN ADC2_Init 2 */

    /* USER CODE END ADC2_Init 2 */

}

/**
  * @brief TIM1 Initialization Function
  * @param None
  * @retval None
  */
static void MX_TIM1_Init(void)
{

    /* USER CODE BEGIN TIM1_Init 0 */

    /* USER CODE END TIM1_Init 0 */

    TIM_ClockConfigTypeDef sClockSourceConfig = {0};
    TIM_MasterConfigTypeDef sMasterConfig = {0};
    TIM_OC_InitTypeDef sConfigOC = {0};
    TIM_BreakDeadTimeConfigTypeDef sBreakDeadTimeConfig = {0};
    TIMEx_BreakInputConfigTypeDef sBreakInputConfig = {0};
    /* USER CODE BEGIN TIM1_Init 1 */

    /* USER CODE END TIM1_Init 1 */
    htim1.Instance = TIM1;
    htim1.Init.Prescaler = 0;
    htim1.Init.CounterMode = TIM_COUNTERMODE_CENTERALIGNED3;
    htim1.Init.Period = 65535;
    htim1.Init.ClockDivision = TIM_CLOCKDIVISION_DIV1;
    htim1.Init.RepetitionCounter = 0;
    htim1.Init.AutoReloadPreload = TIM_AUTORELOAD_PRELOAD_ENABLE;
    if (HAL_TIM_Base_Init(&htim1) != HAL_OK)
    {
        Error_Handler();
    }
    sClockSourceConfig.ClockSource = TIM_CLOCKSOURCE_INTERNAL;
    if (HAL_TIM_ConfigClockSource(&htim1, &sClockSourceConfig) != HAL_OK)
    {
        Error_Handler();
    }
    if (HAL_TIM_PWM_Init(&htim1) != HAL_OK)
    {
        Error_Handler();
    }
    sMasterConfig.MasterOutputTrigger = TIM_TRGO_UPDATE;
    sMasterConfig.MasterOutputTrigger2 = TIM_TRGO2_RESET;
    sMasterConfig.MasterSlaveMode = TIM_MASTERSLAVEMODE_DISABLE;
    if (HAL_TIMEx_MasterConfigSynchronization(&htim1, &sMasterConfig) != HAL_OK)
    {
        Error_Handler();
    }
    sConfigOC.OCMode = TIM_OCMODE_PWM1;
    sConfigOC.Pulse = 0;
    sConfigOC.OCPolarity = TIM_OCPOLARITY_HIGH;
    sConfigOC.OCNPolarity = TIM_OCNPOLARITY_HIGH;
    sConfigOC.OCFastMode = TIM_OCFAST_DISABLE;
    sConfigOC.OCIdleState = TIM_OCIDLESTATE_RESET;
    sConfigOC.OCNIdleState = TIM_OCNIDLESTATE_RESET;
    if (HAL_TIM_PWM_ConfigChannel(&htim1, &sConfigOC, TIM_CHANNEL_1) != HAL_OK)
    {
        Error_Handler();
    }
    sConfigOC.Pulse = 0;
    if (HAL_TIM_PWM_ConfigChannel(&htim1, &sConfigOC, TIM_CHANNEL_2) != HAL_OK)
    {
        Error_Handler();
    }
    sConfigOC.Pulse = 0;
    if (HAL_TIM_PWM_ConfigChannel(&htim1, &sConfigOC, TIM_CHANNEL_3) != HAL_OK)
    {
        Error_Handler();
    }

    sBreakInputConfig.Source = TIM_BREAKINPUTSOURCE_BKIN;
    sBreakInputConfig.Enable = TIM_BREAKINPUTSOURCE_ENABLE;
    sBreakInputConfig.Polarity = TIM_BREAKINPUTSOURCE_POLARITY_HIGH;
    if (HAL_TIMEx_ConfigBreakInput(&htim1, TIM_BREAKINPUT_BRK2, &sBreakInputConfig) != HAL_OK)
    {
        Error_Handler();
    }

    sBreakDeadTimeConfig.OffStateRunMode = TIM_OSSR_ENABLE;
    sBreakDeadTimeConfig.OffStateIDLEMode = TIM_OSSI_ENABLE;
    sBreakDeadTimeConfig.LockLevel = TIM_LOCKLEVEL_OFF;

    sBreakDeadTimeConfig.DeadTime = 0;
    sBreakDeadTimeConfig.BreakState = TIM_BREAK_DISABLE;
    sBreakDeadTimeConfig.BreakPolarity = TIM_BREAKPOLARITY_HIGH;
    sBreakDeadTimeConfig.BreakFilter = 2;//9;
    sBreakDeadTimeConfig.BreakAFMode = TIM_BREAK_AFMODE_INPUT;
    sBreakDeadTimeConfig.Break2State = TIM_BREAK2_ENABLE;
    sBreakDeadTimeConfig.Break2Polarity = TIM_BREAK2POLARITY_LOW;
    sBreakDeadTimeConfig.Break2Filter = 2;//9;
    sBreakDeadTimeConfig.Break2AFMode = TIM_BREAK_AFMODE_INPUT;
    sBreakDeadTimeConfig.AutomaticOutput = TIM_AUTOMATICOUTPUT_DISABLE; //20250620
    if (HAL_TIMEx_ConfigBreakDeadTime(&htim1, &sBreakDeadTimeConfig) != HAL_OK)
    {
        Error_Handler();
    }
    /* USER CODE BEGIN TIM1_Init 2 */
    __HAL_TIM_ENABLE_IT(&htim1, TIM_IT_BREAK);
    /* USER CODE END TIM1_Init 2 */
    HAL_TIM_MspPostInit(&htim1);

}

/**
  * @brief TIM4 Initialization Function
  * @param None
  * @retval None
  */
static void MX_TIM4_Init(void)
{

    /* USER CODE BEGIN TIM4_Init 0 */

    /* USER CODE END TIM4_Init 0 */

    TIM_ClockConfigTypeDef sClockSourceConfig = {0};
    TIM_MasterConfigTypeDef sMasterConfig = {0};

    /* USER CODE BEGIN TIM4_Init 1 */

    /* USER CODE END TIM4_Init 1 */
    htim4.Instance = TIM4;
    htim4.Init.Prescaler = 1;
    htim4.Init.CounterMode = TIM_COUNTERMODE_DOWN;
    htim4.Init.Period = 65499;
    htim4.Init.ClockDivision = TIM_CLOCKDIVISION_DIV1;
    htim4.Init.AutoReloadPreload = TIM_AUTORELOAD_PRELOAD_ENABLE;
    if (HAL_TIM_Base_Init(&htim4) != HAL_OK)
    {
        Error_Handler();
    }
    sClockSourceConfig.ClockSource = TIM_CLOCKSOURCE_INTERNAL;
    if (HAL_TIM_ConfigClockSource(&htim4, &sClockSourceConfig) != HAL_OK)
    {
        Error_Handler();
    }
    sMasterConfig.MasterOutputTrigger = TIM_TRGO_RESET;
    sMasterConfig.MasterSlaveMode = TIM_MASTERSLAVEMODE_DISABLE;
    if (HAL_TIMEx_MasterConfigSynchronization(&htim4, &sMasterConfig) != HAL_OK)
    {
        Error_Handler();
    }
    /* USER CODE BEGIN TIM4_Init 2 */

    /* USER CODE END TIM4_Init 2 */

}

/**
  * Enable DMA controller clock
  */
static void MX_DMA_Init(void)
{

    /* DMA controller clock enable */
    __HAL_RCC_DMAMUX1_CLK_ENABLE();
    __HAL_RCC_DMA1_CLK_ENABLE();
    __HAL_RCC_DMA2_CLK_ENABLE();

    /* DMA1_Channel3_IRQn interrupt configuration */
    HAL_NVIC_SetPriority(DMA2_Channel1_IRQn, 0, 0);
    HAL_NVIC_EnableIRQ(DMA2_Channel1_IRQn);

}

/**
  * @brief GPIO Initialization Function
  * @param None
  * @retval None
  */
static void MX_GPIO_Init(void)
{
    GPIO_InitTypeDef GPIO_InitStruct = {0};

    /* GPIO Ports Clock Enable */
    __HAL_RCC_GPIOC_CLK_ENABLE();
    __HAL_RCC_GPIOF_CLK_ENABLE();
    __HAL_RCC_GPIOA_CLK_ENABLE();
    __HAL_RCC_GPIOB_CLK_ENABLE();
    __HAL_RCC_GPIOE_CLK_ENABLE();
    __HAL_RCC_GPIOD_CLK_ENABLE();
    __HAL_RCC_GPIOG_CLK_ENABLE();


}

/* USER CODE BEGIN 4 */
/******************************************************************************
 * ďż??  ć°ďź HAL_UARTEx_RxEventCallback
 * ďż??  č˝ďź DMA+çŠşé˛ä¸?ć?ĺč°ĺ˝ć°
 * ďż??  ć°ďź UART_HandleTypeDef  *huart   // č§Śĺçä¸˛ďż??
 *          uint16_t             Size    // ćĽćśĺ­č
 * čżĺĺźďź ďż??
 * ďż??  ćł?ďź? 1ďźčżä¸?ć?ĺč°ĺ˝ć°ďźä¸ć?ä¸?ć?ćĺĄĺ˝ć°ďż??ďż˝ćĺˇ§ďźä˝żç¨CubeMXçćçĺˇĽç¨ä¸­ďźä¸­ć?ćĺĄĺ˝ć°ĺˇ˛č?ŤCubeMXĺŽćĺŚĽĺ˝ďźćäť?ĺ?çŽĄéĺĺč°ĺ˝ďż??
 *          2ďźč§ŚĺćĄäťśďźĺ˝DMAćĽćśĺ°ćĺŽĺ­čć°ćśďźćäş§ççŠşé˛ä¸­ć?ćśďźçĄ?äťśĺ°ąäźčŞĺ¨č°ç¨ćŹĺč°ĺ˝ć°ďźć ďż??čżč?äşşĺˇĽč°ç?;
 *          2ďźĺżéĄťä˝żç¨čżä¸?ĺ˝ć°ĺç§°ďźĺ ä¸şĺŽĺ¨CubeMXçććśďźĺˇ˛č??ĺĺĽ˝äşĺç§ĺ˝ć°č°ç?ďż??ďż˝ĺ˝ć°ĺźąĺŽäš(ĺ¨stm32xx_hal_uart.cçĺşďż??); ä¸č?ĺ¨ĺĺźąĺŽäšä¸?ĺ˘ćˇťäťŁç ďźďż˝?ďż˝ćŻéĺć?ĺ˝ďż˝?
 *          3ďźć ďż??čżč?ä¸­ć?ć ĺżçć¸çďźĺŽĺ¨č˘?č°ç¨ĺďźĺˇ˛ćć¸ä¸­ć?çćďż??;
 *          4ďźçćçďż??ćDMA+çŠşé˛ä¸?ć?ćĺĄĺ˝ć°ďźé˝äźçťďż??č°ç¨čżä¸Şĺ˝ć°ďźäťĽĺźčçźĺˇä˝ĺďż??
 *          5ďźĺ¤ć?ĺć°äź čżćĽçĺźčçźĺˇďźĺłĺ?çĽéć?ĺ?ä¸?ä¸˛ĺŁćĽćśćśäşĺ¤ĺ°ĺ­ďż˝?
******************************************************************************/
//void HAL_UART_RxCpltCallback(UART_HandleTypeDef *huart)
//{
//if (huart == &huart1)                                                                    // ĺ¤ć­ä¸˛ĺŁ
//{
//    __HAL_UNLOCK(huart);                                                                 // č§Łéä¸˛ĺŁçśďż˝??
//    commRxSize = huart1.RxXferSize - huart1.hdmarx->Instance->CNDTR;
//    HAL_UART_Receive_DMA(&huart1, Readbuf, 8);   // ĺć?Ąďż˝?ĺŻDMAçŠşé˛ä¸?ć?; ćŻĺ˝ćĽćśĺŽćĺŽéżĺşŚďźćďż˝?ďż˝äş§ççŠşé˛ä¸­ć?ćśďźĺ°ąäźćĽĺ°čżďż˝?
//    commStatus = SCI_RECEIVE_OK;

//}
//}


void User_ADC_Init(void)
{
    //#: must add HAL_ADC_DeInit to restore original state
    HAL_ADC_DeInit(&hadc1);
    HAL_ADC_DeInit(&hadc2);
    //#: reinitialize adc
    MX_ADC1_Init();
    MX_ADC2_Init();
}


/************************ (C) COPYRIGHT STMicroelectronics *****END OF FILE****/
